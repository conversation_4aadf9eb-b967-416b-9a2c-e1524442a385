<?php
/**
 * ملف فحص حالة قاعدة البيانات
 * Database Status Check
 */

echo "<h1>فحص حالة قاعدة البيانات - Database Status Check</h1>";

$db_host = "localhost";
$db_user = "root";
$db_pass = "";
$db_name = "zero";

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $conn = new mysqli($db_host, $db_user, $db_pass);
    $conn->set_charset("utf8mb4");
    
    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بـ MySQL: " . $conn->connect_error);
    }
    
    echo "<p style='color: green;'>✅ تم الاتصال بـ MySQL بنجاح</p>";
    
    // التحقق من وجود قاعدة البيانات
    $result = $conn->query("SHOW DATABASES LIKE 'zero'");
    
    if ($result && $result->num_rows > 0) {
        echo "<p style='color: green;'>✅ قاعدة البيانات 'zero' موجودة</p>";
        
        // الاتصال بقاعدة البيانات
        $conn->select_db($db_name);
        
        // فحص الجداول المطلوبة
        $required_tables = array(
            'users' => 'المستخدمين',
            'categories' => 'التصنيفات',
            'products' => 'المنتجات',
            'customers' => 'العملاء',
            'suppliers' => 'الموردين',
            'sales' => 'المبيعات',
            'sale_items' => 'تفاصيل المبيعات',
            'purchases' => 'المشتريات',
            'purchase_items' => 'تفاصيل المشتريات',
            'expenses' => 'المصروفات',
            'treasury_transactions' => 'حركات الخزينة',
            'settings' => 'الإعدادات'
        );
        
        echo "<h3>حالة الجداول:</h3>";
        $missing_tables = array();
        
        foreach ($required_tables as $table => $arabic_name) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            
            if ($result && $result->num_rows > 0) {
                // عد الصفوف في الجدول
                $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
                $count = 0;
                if ($count_result) {
                    $count_row = $count_result->fetch_assoc();
                    $count = $count_row['count'];
                }
                
                echo "<p style='color: green;'>✅ جدول '$table' ($arabic_name) موجود - عدد الصفوف: $count</p>";
            } else {
                echo "<p style='color: red;'>❌ جدول '$table' ($arabic_name) غير موجود</p>";
                $missing_tables[] = $table;
            }
        }
        
        // التحقق من المستخدم الافتراضي
        if (in_array('users', array_keys($required_tables)) && !in_array('users', $missing_tables)) {
            echo "<h3>المستخدمين:</h3>";
            $result = $conn->query("SELECT username, name, role FROM users");
            
            if ($result && $result->num_rows > 0) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr style='background: #f0f0f0;'><th>اسم المستخدم</th><th>الاسم</th><th>الدور</th></tr>";
                
                while ($user = $result->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>" . $user['username'] . "</td>";
                    echo "<td>" . $user['name'] . "</td>";
                    echo "<td>" . $user['role'] . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p style='color: orange;'>⚠️ لا يوجد مستخدمين في النظام</p>";
            }
        }
        
        // عرض الإجراءات المطلوبة
        if (!empty($missing_tables)) {
            echo "<hr>";
            echo "<h3 style='color: red;'>⚠️ إجراءات مطلوبة:</h3>";
            echo "<p>الجداول التالية مفقودة: " . implode(', ', $missing_tables) . "</p>";
            echo "<p><a href='create-tables.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إنشاء الجداول المفقودة</a></p>";
        } else {
            echo "<hr>";
            echo "<h3 style='color: green;'>🎉 قاعدة البيانات جاهزة للاستخدام!</h3>";
            echo "<p><a href='login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تسجيل الدخول</a></p>";
            echo "<p><strong>بيانات الدخول:</strong> admin / admin</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ قاعدة البيانات 'zero' غير موجودة</p>";
        echo "<p><a href='create-database.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إنشاء قاعدة البيانات</a></p>";
    }
    
    $conn->close();
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    
    echo "<h3>خطوات استكشاف الأخطاء:</h3>";
    echo "<ol>";
    echo "<li>تأكد من تشغيل WAMP وأن الأيقونة خضراء</li>";
    echo "<li>تأكد من تشغيل خدمة MySQL</li>";
    echo "<li>تحقق من بيانات الاتصال في config/db_config.php</li>";
    echo "<li>جرب الوصول إلى phpMyAdmin: <a href='http://localhost/phpmyadmin'>http://localhost/phpmyadmin</a></li>";
    echo "</ol>";
}

echo "<hr>";
echo "<h3>روابط مفيدة:</h3>";
echo "<ul>";
echo "<li><a href='create-database.php'>إنشاء قاعدة البيانات</a></li>";
echo "<li><a href='create-tables.php'>إنشاء الجداول</a></li>";
echo "<li><a href='test-connection.php'>اختبار الاتصال</a></li>";
echo "<li><a href='login.php'>تسجيل الدخول</a></li>";
echo "<li><a href='http://localhost/phpmyadmin' target='_blank'>phpMyAdmin</a></li>";
echo "</ul>";
?>

<?php
/**
 * ملف إصلاح جميع الأعمدة المفقودة في النظام
 * Fix All Missing Columns in System
 */

echo "<h1>إصلاح جميع الأعمدة المفقودة في النظام</h1>";
echo "<h2>Fix All Missing Columns in System</h2>";

$db_host = "localhost";
$db_user = "root";
$db_pass = "";
$db_name = "zero";

try {
    // الاتصال بقاعدة البيانات
    $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
    $conn->set_charset("utf8mb4");
    
    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }
    
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    $fixes_applied = 0;
    $errors_count = 0;
    
    // 1. إصلاح جدول المنتجات (products)
    echo "<h2>1. إصلاح جدول المنتجات (products)</h2>";
    
    $products_columns = array(
        'unit' => "ADD COLUMN unit VARCHAR(20) DEFAULT 'قطعة' AFTER selling_price",
        'sku' => "ADD COLUMN sku VARCHAR(50) DEFAULT NULL AFTER barcode",
        'weight' => "ADD COLUMN weight DECIMAL(8,3) DEFAULT NULL AFTER unit",
        'dimensions' => "ADD COLUMN dimensions VARCHAR(100) DEFAULT NULL AFTER weight",
        'warranty_period' => "ADD COLUMN warranty_period INT DEFAULT NULL AFTER dimensions",
        'supplier_id' => "ADD COLUMN supplier_id INT DEFAULT NULL AFTER warranty_period",
        'reorder_level' => "ADD COLUMN reorder_level INT DEFAULT NULL AFTER min_stock",
        'location' => "ADD COLUMN location VARCHAR(100) DEFAULT NULL AFTER reorder_level",
        'is_active' => "ADD COLUMN is_active TINYINT(1) DEFAULT 1 AFTER description",
        'cost_price' => "ADD COLUMN cost_price DECIMAL(10,2) DEFAULT 0.00 AFTER purchase_price",
        'profit_margin' => "ADD COLUMN profit_margin DECIMAL(5,2) DEFAULT 0.00 AFTER cost_price"
    );
    
    foreach ($products_columns as $column => $sql) {
        $check = $conn->query("SHOW COLUMNS FROM products LIKE '$column'");
        if (!$check || $check->num_rows == 0) {
            if ($conn->query("ALTER TABLE products $sql") === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column' لجدول products</p>";
                $fixes_applied++;
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود '$column': " . $conn->error . "</p>";
                $errors_count++;
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column' موجود بالفعل في جدول products</p>";
        }
    }
    
    // 2. إصلاح جدول العملاء (customers)
    echo "<h2>2. إصلاح جدول العملاء (customers)</h2>";
    
    $customers_columns = array(
        'balance' => "ADD COLUMN balance DECIMAL(10,2) DEFAULT 0.00 AFTER email",
        'notes' => "ADD COLUMN notes TEXT DEFAULT NULL AFTER balance",
        'credit_limit' => "ADD COLUMN credit_limit DECIMAL(10,2) DEFAULT 0.00 AFTER notes",
        'customer_type' => "ADD COLUMN customer_type ENUM('individual','company') DEFAULT 'individual' AFTER credit_limit",
        'tax_number' => "ADD COLUMN tax_number VARCHAR(50) DEFAULT NULL AFTER customer_type",
        'is_active' => "ADD COLUMN is_active TINYINT(1) DEFAULT 1 AFTER tax_number"
    );
    
    foreach ($customers_columns as $column => $sql) {
        $check = $conn->query("SHOW COLUMNS FROM customers LIKE '$column'");
        if (!$check || $check->num_rows == 0) {
            if ($conn->query("ALTER TABLE customers $sql") === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column' لجدول customers</p>";
                $fixes_applied++;
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود '$column': " . $conn->error . "</p>";
                $errors_count++;
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column' موجود بالفعل في جدول customers</p>";
        }
    }
    
    // 3. إصلاح جدول الموردين (suppliers)
    echo "<h2>3. إصلاح جدول الموردين (suppliers)</h2>";
    
    $suppliers_columns = array(
        'balance' => "ADD COLUMN balance DECIMAL(10,2) DEFAULT 0.00 AFTER email",
        'company' => "ADD COLUMN company VARCHAR(100) DEFAULT NULL AFTER name",
        'contact_person' => "ADD COLUMN contact_person VARCHAR(100) DEFAULT NULL AFTER company",
        'notes' => "ADD COLUMN notes TEXT DEFAULT NULL AFTER balance",
        'credit_limit' => "ADD COLUMN credit_limit DECIMAL(10,2) DEFAULT 0.00 AFTER notes",
        'tax_number' => "ADD COLUMN tax_number VARCHAR(50) DEFAULT NULL AFTER credit_limit",
        'is_active' => "ADD COLUMN is_active TINYINT(1) DEFAULT 1 AFTER tax_number"
    );
    
    foreach ($suppliers_columns as $column => $sql) {
        $check = $conn->query("SHOW COLUMNS FROM suppliers LIKE '$column'");
        if (!$check || $check->num_rows == 0) {
            if ($conn->query("ALTER TABLE suppliers $sql") === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column' لجدول suppliers</p>";
                $fixes_applied++;
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود '$column': " . $conn->error . "</p>";
                $errors_count++;
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column' موجود بالفعل في جدول suppliers</p>";
        }
    }
    
    // 4. إصلاح جدول المبيعات (sales)
    echo "<h2>4. إصلاح جدول المبيعات (sales)</h2>";
    
    $sales_columns = array(
        'sale_number' => "ADD COLUMN sale_number VARCHAR(50) DEFAULT NULL AFTER id",
        'discount' => "ADD COLUMN discount DECIMAL(10,2) DEFAULT 0.00 AFTER total_amount",
        'discount_amount' => "ADD COLUMN discount_amount DECIMAL(10,2) DEFAULT 0.00 AFTER discount",
        'tax_amount' => "ADD COLUMN tax_amount DECIMAL(10,2) DEFAULT 0.00 AFTER discount_amount",
        'final_amount' => "ADD COLUMN final_amount DECIMAL(10,2) DEFAULT 0.00 AFTER tax_amount",
        'paid_amount' => "ADD COLUMN paid_amount DECIMAL(10,2) DEFAULT 0.00 AFTER final_amount",
        'payment_status' => "ADD COLUMN payment_status ENUM('paid','partial','unpaid') DEFAULT 'unpaid' AFTER paid_amount",
        'payment_method' => "ADD COLUMN payment_method ENUM('cash','card','bank_transfer','check','credit') DEFAULT 'cash' AFTER payment_status",
        'notes' => "ADD COLUMN notes TEXT DEFAULT NULL AFTER payment_method"
    );
    
    foreach ($sales_columns as $column => $sql) {
        $check = $conn->query("SHOW COLUMNS FROM sales LIKE '$column'");
        if (!$check || $check->num_rows == 0) {
            if ($conn->query("ALTER TABLE sales $sql") === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column' لجدول sales</p>";
                $fixes_applied++;
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود '$column': " . $conn->error . "</p>";
                $errors_count++;
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column' موجود بالفعل في جدول sales</p>";
        }
    }
    
    // 5. إصلاح جدول المشتريات (purchases)
    echo "<h2>5. إصلاح جدول المشتريات (purchases)</h2>";
    
    $purchases_columns = array(
        'purchase_number' => "ADD COLUMN purchase_number VARCHAR(50) DEFAULT NULL AFTER id",
        'discount' => "ADD COLUMN discount DECIMAL(10,2) DEFAULT 0.00 AFTER total_amount",
        'discount_amount' => "ADD COLUMN discount_amount DECIMAL(10,2) DEFAULT 0.00 AFTER discount",
        'tax_amount' => "ADD COLUMN tax_amount DECIMAL(10,2) DEFAULT 0.00 AFTER discount_amount",
        'final_amount' => "ADD COLUMN final_amount DECIMAL(10,2) DEFAULT 0.00 AFTER tax_amount",
        'paid_amount' => "ADD COLUMN paid_amount DECIMAL(10,2) DEFAULT 0.00 AFTER final_amount",
        'payment_status' => "ADD COLUMN payment_status ENUM('paid','partial','unpaid') DEFAULT 'unpaid' AFTER paid_amount",
        'payment_method' => "ADD COLUMN payment_method ENUM('cash','card','bank_transfer','check','credit') DEFAULT 'cash' AFTER payment_status"
    );
    
    foreach ($purchases_columns as $column => $sql) {
        $check = $conn->query("SHOW COLUMNS FROM purchases LIKE '$column'");
        if (!$check || $check->num_rows == 0) {
            if ($conn->query("ALTER TABLE purchases $sql") === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column' لجدول purchases</p>";
                $fixes_applied++;
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود '$column': " . $conn->error . "</p>";
                $errors_count++;
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column' موجود بالفعل في جدول purchases</p>";
        }
    }
    
    // 6. إصلاح جدول المصروفات (expenses)
    echo "<h2>6. إصلاح جدول المصروفات (expenses)</h2>";
    
    $expenses_columns = array(
        'expense_type' => "ADD COLUMN expense_type VARCHAR(100) DEFAULT 'عام' AFTER category",
        'receipt_number' => "ADD COLUMN receipt_number VARCHAR(50) DEFAULT NULL AFTER expense_type",
        'notes' => "ADD COLUMN notes TEXT DEFAULT NULL AFTER receipt_number",
        'payment_method' => "ADD COLUMN payment_method ENUM('cash','card','bank_transfer','check') DEFAULT 'cash' AFTER notes"
    );
    
    foreach ($expenses_columns as $column => $sql) {
        $check = $conn->query("SHOW COLUMNS FROM expenses LIKE '$column'");
        if (!$check || $check->num_rows == 0) {
            if ($conn->query("ALTER TABLE expenses $sql") === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column' لجدول expenses</p>";
                $fixes_applied++;
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود '$column': " . $conn->error . "</p>";
                $errors_count++;
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column' موجود بالفعل في جدول expenses</p>";
        }
    }
    
    // 7. إصلاح جدول تفاصيل المبيعات (sale_items)
    echo "<h2>7. إصلاح جدول تفاصيل المبيعات (sale_items)</h2>";
    
    $sale_items_columns = array(
        'discount_amount' => "ADD COLUMN discount_amount DECIMAL(10,2) DEFAULT 0.00 AFTER total_price",
        'tax_amount' => "ADD COLUMN tax_amount DECIMAL(10,2) DEFAULT 0.00 AFTER discount_amount"
    );
    
    foreach ($sale_items_columns as $column => $sql) {
        $check = $conn->query("SHOW COLUMNS FROM sale_items LIKE '$column'");
        if (!$check || $check->num_rows == 0) {
            if ($conn->query("ALTER TABLE sale_items $sql") === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column' لجدول sale_items</p>";
                $fixes_applied++;
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود '$column': " . $conn->error . "</p>";
                $errors_count++;
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column' موجود بالفعل في جدول sale_items</p>";
        }
    }
    
    // 8. إصلاح جدول تفاصيل المشتريات (purchase_items)
    echo "<h2>8. إصلاح جدول تفاصيل المشتريات (purchase_items)</h2>";
    
    $purchase_items_columns = array(
        'discount_amount' => "ADD COLUMN discount_amount DECIMAL(10,2) DEFAULT 0.00 AFTER total_price",
        'tax_amount' => "ADD COLUMN tax_amount DECIMAL(10,2) DEFAULT 0.00 AFTER discount_amount"
    );
    
    foreach ($purchase_items_columns as $column => $sql) {
        $check = $conn->query("SHOW COLUMNS FROM purchase_items LIKE '$column'");
        if (!$check || $check->num_rows == 0) {
            if ($conn->query("ALTER TABLE purchase_items $sql") === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column' لجدول purchase_items</p>";
                $fixes_applied++;
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود '$column': " . $conn->error . "</p>";
                $errors_count++;
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column' موجود بالفعل في جدول purchase_items</p>";
        }
    }
    
    // تحديث البيانات الموجودة
    echo "<h2>9. تحديث البيانات الموجودة</h2>";
    
    // تحديث المنتجات
    $conn->query("UPDATE products SET cost_price = purchase_price WHERE cost_price = 0 OR cost_price IS NULL");
    $conn->query("UPDATE products SET is_active = 1 WHERE is_active IS NULL");
    echo "<p style='color: green;'>✅ تم تحديث بيانات المنتجات</p>";
    
    // تحديث العملاء
    $conn->query("UPDATE customers SET is_active = 1 WHERE is_active IS NULL");
    echo "<p style='color: green;'>✅ تم تحديث بيانات العملاء</p>";
    
    // تحديث الموردين
    $conn->query("UPDATE suppliers SET is_active = 1 WHERE is_active IS NULL");
    $conn->query("UPDATE suppliers SET company = name WHERE company IS NULL OR company = ''");
    echo "<p style='color: green;'>✅ تم تحديث بيانات الموردين</p>";
    
    // تحديث المبيعات
    $conn->query("UPDATE sales SET final_amount = total_amount WHERE final_amount = 0 OR final_amount IS NULL");
    $conn->query("UPDATE sales SET paid_amount = total_amount WHERE paid_amount = 0 OR paid_amount IS NULL");
    $conn->query("UPDATE sales SET payment_status = 'paid' WHERE payment_status IS NULL");
    $conn->query("UPDATE sales SET payment_method = 'cash' WHERE payment_method IS NULL");
    $conn->query("UPDATE sales SET sale_number = CONCAT('S', LPAD(id, 6, '0')) WHERE sale_number IS NULL OR sale_number = ''");
    echo "<p style='color: green;'>✅ تم تحديث بيانات المبيعات</p>";
    
    // تحديث المشتريات
    $conn->query("UPDATE purchases SET final_amount = total_amount WHERE final_amount = 0 OR final_amount IS NULL");
    $conn->query("UPDATE purchases SET paid_amount = total_amount WHERE paid_amount = 0 OR paid_amount IS NULL");
    $conn->query("UPDATE purchases SET payment_status = 'paid' WHERE payment_status IS NULL");
    $conn->query("UPDATE purchases SET payment_method = 'cash' WHERE payment_method IS NULL");
    $conn->query("UPDATE purchases SET purchase_number = CONCAT('P', LPAD(id, 6, '0')) WHERE purchase_number IS NULL OR purchase_number = ''");
    echo "<p style='color: green;'>✅ تم تحديث بيانات المشتريات</p>";
    
    // تحديث المصروفات
    $conn->query("UPDATE expenses SET expense_type = category WHERE expense_type = 'عام' OR expense_type IS NULL");
    $conn->query("UPDATE expenses SET payment_method = 'cash' WHERE payment_method IS NULL");
    echo "<p style='color: green;'>✅ تم تحديث بيانات المصروفات</p>";
    
    $conn->close();
    
    echo "<hr>";
    echo "<h2>تقرير الإصلاحات النهائي</h2>";
    
    if ($errors_count == 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h3 style='color: #155724;'>🎉 تم إصلاح جميع الأعمدة المفقودة بنجاح!</h3>";
        echo "<p>تم تطبيق $fixes_applied إصلاح</p>";
        echo "</div>";
        
        echo "<h3>يمكنك الآن استخدام جميع صفحات النظام بدون أخطاء:</h3>";
        echo "<ul>";
        echo "<li><a href='pages/sales/add.php' style='color: blue;'>➕ إضافة فاتورة مبيعات</a></li>";
        echo "<li><a href='pages/purchases/add.php' style='color: blue;'>➕ إضافة فاتورة مشتريات</a></li>";
        echo "<li><a href='pages/products/add.php' style='color: blue;'>➕ إضافة منتج جديد</a></li>";
        echo "<li><a href='pages/customers/add.php' style='color: blue;'>➕ إضافة عميل جديد</a></li>";
        echo "<li><a href='pages/suppliers/add.php' style='color: blue;'>➕ إضافة مورد جديد</a></li>";
        echo "<li><a href='pages/expenses/add.php' style='color: blue;'>➕ إضافة مصروف جديد</a></li>";
        echo "<li><a href='index.php' style='color: blue;'>🏠 الصفحة الرئيسية</a></li>";
        echo "</ul>";
        
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h3 style='color: #721c24;'>⚠️ تم تطبيق $fixes_applied إصلاح مع $errors_count خطأ</h3>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

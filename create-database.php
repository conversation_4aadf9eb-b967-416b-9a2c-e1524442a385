<?php
/**
 * ملف إنشاء قاعدة البيانات
 * Database Creation Script
 */

echo "<h1>إنشاء قاعدة البيانات - Database Creation</h1>";

// معلومات الاتصال بقاعدة البيانات (بدون اسم قاعدة البيانات)
$db_host = "localhost";
$db_user = "root";
$db_pass = "";

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $conn = new mysqli($db_host, $db_user, $db_pass);
    $conn->set_charset("utf8mb4");
    
    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بـ MySQL: " . $conn->connect_error);
    }
    
    echo "<p style='color: green;'>✅ تم الاتصال بـ MySQL بنجاح</p>";
    
    // إنشاء قاعدة البيانات
    $sql = "CREATE DATABASE IF NOT EXISTS `zero` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    
    if ($conn->query($sql) === TRUE) {
        echo "<p style='color: green;'>✅ تم إنشاء قاعدة البيانات 'zero' بنجاح</p>";
    } else {
        throw new Exception("خطأ في إنشاء قاعدة البيانات: " . $conn->error);
    }
    
    // اختيار قاعدة البيانات
    $conn->select_db("zero");
    echo "<p style='color: green;'>✅ تم اختيار قاعدة البيانات 'zero'</p>";
    
    // قراءة ملف SQL وتنفيذه
    $sql_file = "database/zero.sql";
    
    if (!file_exists($sql_file)) {
        throw new Exception("ملف قاعدة البيانات غير موجود: " . $sql_file);
    }
    
    $sql_content = file_get_contents($sql_file);
    
    if ($sql_content === false) {
        throw new Exception("فشل في قراءة ملف قاعدة البيانات");
    }
    
    echo "<p style='color: blue;'>📖 تم قراءة ملف قاعدة البيانات</p>";
    
    // تقسيم الاستعلامات
    $queries = explode(';', $sql_content);
    $success_count = 0;
    $error_count = 0;
    
    foreach ($queries as $query) {
        $query = trim($query);
        
        // تجاهل الاستعلامات الفارغة والتعليقات
        if (empty($query) || strpos($query, '--') === 0 || strpos($query, '/*') === 0) {
            continue;
        }
        
        if ($conn->query($query) === TRUE) {
            $success_count++;
        } else {
            $error_count++;
            echo "<p style='color: orange;'>⚠️ خطأ في الاستعلام: " . $conn->error . "</p>";
            echo "<p style='color: gray;'>الاستعلام: " . substr($query, 0, 100) . "...</p>";
        }
    }
    
    echo "<p style='color: green;'>✅ تم تنفيذ $success_count استعلام بنجاح</p>";
    
    if ($error_count > 0) {
        echo "<p style='color: orange;'>⚠️ فشل في تنفيذ $error_count استعلام</p>";
    }
    
    // التحقق من الجداول المنشأة
    echo "<h3>الجداول المنشأة:</h3>";
    $result = $conn->query("SHOW TABLES");
    
    if ($result && $result->num_rows > 0) {
        echo "<ul>";
        while ($row = $result->fetch_array()) {
            echo "<li style='color: green;'>✅ " . $row[0] . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ لم يتم إنشاء أي جداول</p>";
    }
    
    // التحقق من المستخدم الافتراضي
    echo "<h3>التحقق من المستخدم الافتراضي:</h3>";
    $result = $conn->query("SELECT username, name, role FROM users WHERE username = 'admin'");
    
    if ($result && $result->num_rows > 0) {
        $user = $result->fetch_assoc();
        echo "<p style='color: green;'>✅ المستخدم الافتراضي موجود:</p>";
        echo "<ul>";
        echo "<li>اسم المستخدم: " . $user['username'] . "</li>";
        echo "<li>الاسم: " . $user['name'] . "</li>";
        echo "<li>الدور: " . $user['role'] . "</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ المستخدم الافتراضي غير موجود</p>";
    }
    
    $conn->close();
    
    echo "<hr>";
    echo "<h3 style='color: green;'>🎉 تم إنشاء قاعدة البيانات بنجاح!</h3>";
    echo "<p>يمكنك الآن استخدام النظام:</p>";
    echo "<ul>";
    echo "<li><a href='login.php' style='color: blue;'>🔐 تسجيل الدخول</a></li>";
    echo "<li><a href='test-connection.php' style='color: blue;'>🧪 اختبار النظام</a></li>";
    echo "<li><a href='index.php' style='color: blue;'>🏠 الصفحة الرئيسية</a></li>";
    echo "</ul>";
    
    echo "<h4>بيانات الدخول:</h4>";
    echo "<ul>";
    echo "<li><strong>اسم المستخدم:</strong> admin</li>";
    echo "<li><strong>كلمة المرور:</strong> admin</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    echo "<p>تأكد من:</p>";
    echo "<ul>";
    echo "<li>تشغيل خدمة MySQL في WAMP</li>";
    echo "<li>صحة بيانات الاتصال</li>";
    echo "<li>وجود ملف database/zero.sql</li>";
    echo "</ul>";
}
?>

<?php
/**
 * ملف إصلاح أخطاء htmlspecialchars في النظام
 * Fix htmlspecialchars Errors in System
 */

echo "<h1>إصلاح أخطاء htmlspecialchars في النظام</h1>";
echo "<h2>Fix htmlspecialchars Errors in System</h2>";

try {
    echo "<p style='color: green;'>✅ بدء عملية إصلاح أخطاء htmlspecialchars</p>";
    
    // قائمة الملفات التي تحتاج إصلاح
    $files_to_fix = array(
        'pages/sales/print.php',
        'pages/sales/view.php',
        'pages/sales/index.php',
        'pages/purchases/print.php',
        'pages/purchases/view.php',
        'pages/purchases/index.php',
        'pages/products/index.php',
        'pages/products/view.php',
        'pages/customers/index.php',
        'pages/customers/view.php',
        'pages/suppliers/index.php',
        'pages/suppliers/view.php',
        'pages/expenses/index.php',
        'pages/treasury/index.php'
    );
    
    $fixes_applied = 0;
    $files_processed = 0;
    
    foreach ($files_to_fix as $file_path) {
        if (!file_exists($file_path)) {
            echo "<p style='color: orange;'>⚠️ الملف غير موجود: $file_path</p>";
            continue;
        }
        
        echo "<h3>معالجة الملف: $file_path</h3>";
        
        // قراءة محتوى الملف
        $content = file_get_contents($file_path);
        $original_content = $content;
        
        // أنماط الإصلاح المختلفة
        $patterns = array(
            // إصلاح htmlspecialchars مع القيم الفارغة
            '/htmlspecialchars\(\$([^)]+)\)/' => 'htmlspecialchars($1 ?? \'\')',
            '/htmlspecialchars\(\$([^)]+)\[([^\]]+)\]\)/' => 'htmlspecialchars($1[$2] ?? \'\')',
            '/htmlspecialchars\(\$([^)]+)\[\'([^\']+)\'\]\)/' => 'htmlspecialchars($1[\'$2\'] ?? \'\')',
            '/htmlspecialchars\(\$([^)]+)\["([^"]+)"\]\)/' => 'htmlspecialchars($1["$2"] ?? \'\')',
        );
        
        $file_fixes = 0;
        
        foreach ($patterns as $pattern => $replacement) {
            $new_content = preg_replace($pattern, $replacement, $content);
            if ($new_content !== $content) {
                $matches = preg_match_all($pattern, $content);
                $file_fixes += $matches;
                $content = $new_content;
            }
        }
        
        // إصلاحات خاصة لحالات معينة
        $special_fixes = array(
            // إصلاح أسماء العملاء الفارغة
            'htmlspecialchars($sale[\'customer_name\'] ?? \'\')' => 'htmlspecialchars($sale[\'customer_name\'] ?? \'عميل نقدي\')',
            'htmlspecialchars($purchase[\'supplier_name\'] ?? \'\')' => 'htmlspecialchars($purchase[\'supplier_name\'] ?? \'مورد غير محدد\')',
            'htmlspecialchars($customer[\'name\'] ?? \'\')' => 'htmlspecialchars($customer[\'name\'] ?? \'غير محدد\')',
            'htmlspecialchars($supplier[\'name\'] ?? \'\')' => 'htmlspecialchars($supplier[\'name\'] ?? \'غير محدد\')',
            'htmlspecialchars($product[\'name\'] ?? \'\')' => 'htmlspecialchars($product[\'name\'] ?? \'منتج غير محدد\')',
        );
        
        foreach ($special_fixes as $search => $replace) {
            if (strpos($content, $search) !== false) {
                $content = str_replace($search, $replace, $content);
                $file_fixes++;
            }
        }
        
        // حفظ الملف إذا تم تعديله
        if ($content !== $original_content) {
            if (file_put_contents($file_path, $content)) {
                echo "<p style='color: green;'>✅ تم إصلاح $file_fixes خطأ في الملف</p>";
                $fixes_applied += $file_fixes;
            } else {
                echo "<p style='color: red;'>❌ فشل في حفظ الملف</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ لا توجد أخطاء htmlspecialchars في هذا الملف</p>";
        }
        
        $files_processed++;
    }
    
    echo "<hr>";
    echo "<h2>تقرير الإصلاحات</h2>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>🎉 تم الانتهاء من إصلاح أخطاء htmlspecialchars!</h3>";
    echo "<p><strong>الملفات المعالجة:</strong> $files_processed</p>";
    echo "<p><strong>الأخطاء المصلحة:</strong> $fixes_applied</p>";
    echo "</div>";
    
    // اختبار الصفحات المصلحة
    echo "<h2>اختبار الصفحات المصلحة</h2>";
    
    $test_pages = array(
        array('طباعة فاتورة مبيعات', 'pages/sales/print.php?id=1', '🖨️'),
        array('عرض فاتورة مبيعات', 'pages/sales/view.php?id=1', '👁️'),
        array('طباعة فاتورة مشتريات', 'pages/purchases/print.php?id=1', '🖨️'),
        array('عرض فاتورة مشتريات', 'pages/purchases/view.php?id=1', '👁️'),
        array('قائمة المنتجات', 'pages/products/index.php', '📦'),
        array('قائمة العملاء', 'pages/customers/index.php', '👥'),
        array('قائمة الموردين', 'pages/suppliers/index.php', '📋'),
        array('قائمة المصروفات', 'pages/expenses/index.php', '💸')
    );
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    foreach ($test_pages as $page) {
        echo "<div style='background: white; border: 1px solid #ddd; padding: 15px; text-align: center; border-radius: 8px;'>";
        echo "<div style='font-size: 2em; margin-bottom: 10px;'>{$page[2]}</div>";
        echo "<h4 style='margin: 10px 0;'>{$page[0]}</h4>";
        echo "<a href='{$page[1]}' target='_blank' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; display: inline-block;'>اختبار الصفحة</a>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // نصائح لتجنب الأخطاء مستقبلاً
    echo "<h2>نصائح لتجنب أخطاء htmlspecialchars مستقبلاً</h2>";
    
    $tips = array(
        '✅ استخدم دائماً ?? \'\' مع htmlspecialchars للقيم التي قد تكون فارغة',
        '✅ تحقق من وجود البيانات قبل عرضها باستخدام !empty() أو isset()',
        '✅ استخدم قيم افتراضية مناسبة مثل "عميل نقدي" بدلاً من سلسلة فارغة',
        '✅ اختبر الصفحات مع بيانات فارغة للتأكد من عدم ظهور أخطاء',
        '✅ استخدم دالة مساعدة مثل safe_output() للتعامل مع القيم الفارغة'
    );
    
    echo "<ul style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; border-radius: 8px;'>";
    foreach ($tips as $tip) {
        echo "<li style='margin: 10px 0;'>$tip</li>";
    }
    echo "</ul>";
    
    // إنشاء دالة مساعدة
    echo "<h2>دالة مساعدة مقترحة</h2>";
    
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>يمكنك إضافة هذه الدالة إلى includes/functions.php:</h4>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;'>";
    echo htmlspecialchars('
/**
 * دالة آمنة لعرض النصوص مع تجنب أخطاء htmlspecialchars
 * @param mixed $value القيمة المراد عرضها
 * @param string $default القيمة الافتراضية
 * @return string النص المنسق بأمان
 */
function safe_output($value, $default = \'\') {
    if ($value === null || $value === \'\') {
        return htmlspecialchars($default);
    }
    return htmlspecialchars($value);
}

// أمثلة للاستخدام:
// echo safe_output($customer[\'name\'], \'عميل نقدي\');
// echo safe_output($product[\'description\'], \'لا يوجد وصف\');
');
    echo "</pre>";
    echo "</div>";
    
    echo "<h3>روابط سريعة للاختبار:</h3>";
    echo "<ul>";
    echo "<li><a href='pages/sales/print.php?id=1' target='_blank'>اختبار طباعة فاتورة مبيعات</a></li>";
    echo "<li><a href='pages/sales/print.php?id=3' target='_blank'>اختبار طباعة فاتورة عميل نقدي</a></li>";
    echo "<li><a href='pages/purchases/print.php?id=1' target='_blank'>اختبار طباعة فاتورة مشتريات</a></li>";
    echo "<li><a href='pages/sales/index.php' target='_blank'>اختبار قائمة المبيعات</a></li>";
    echo "<li><a href='pages/customers/index.php' target='_blank'>اختبار قائمة العملاء</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

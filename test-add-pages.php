<?php
/**
 * ملف اختبار صفحات الإضافة
 * Test Add Pages
 */

echo "<h1>اختبار صفحات الإضافة - Test Add Pages</h1>";

require_once 'config/db_config.php';

session_start();
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['user_name'] = 'مدير النظام';
$_SESSION['user_role'] = 'admin';
$_SESSION['role'] = 'admin';

try {
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // اختبار صفحات الإضافة
    echo "<h2>اختبار صفحات الإضافة</h2>";
    
    $add_pages = array(
        array('إضافة فاتورة مبيعات', 'pages/sales/add.php', '💰', 'إنشاء فاتورة مبيعات جديدة'),
        array('إضافة فاتورة مشتريات', 'pages/purchases/add.php', '🛒', 'إنشاء فاتورة مشتريات جديدة'),
        array('إضافة منتج جديد', 'pages/products/add.php', '📦', 'إضافة منتج جديد للمخزون'),
        array('إضافة عميل جديد', 'pages/customers/add.php', '👥', 'إضافة عميل جديد للنظام'),
        array('إضافة مورد جديد', 'pages/suppliers/add.php', '📋', 'إضافة مورد جديد للنظام'),
        array('إضافة مصروف جديد', 'pages/expenses/add.php', '💸', 'تسجيل مصروف جديد'),
        array('إضافة تصنيف جديد', 'pages/categories/add.php', '🏷️', 'إضافة تصنيف منتجات جديد'),
        array('إضافة مستخدم جديد', 'pages/users/add.php', '👤', 'إضافة مستخدم جديد للنظام')
    );
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0;'>";
    
    foreach ($add_pages as $page) {
        $page_exists = file_exists($page[1]);
        $status_color = $page_exists ? '#28a745' : '#dc3545';
        $status_text = $page_exists ? '✅ متاح' : '❌ غير موجود';
        
        echo "<div style='background: white; border: 1px solid #ddd; padding: 20px; border-radius: 10px; text-align: center; box-shadow: 0 4px 6px rgba(0,0,0,0.1);'>";
        echo "<div style='font-size: 3em; margin-bottom: 15px;'>{$page[2]}</div>";
        echo "<h4 style='margin: 15px 0; color: #333;'>{$page[0]}</h4>";
        echo "<p style='color: #666; margin: 15px 0; font-size: 0.9em;'>{$page[3]}</p>";
        echo "<div style='margin: 15px 0;'>";
        echo "<span style='color: $status_color; font-weight: bold;'>$status_text</span>";
        echo "</div>";
        
        if ($page_exists) {
            echo "<a href='{$page[1]}' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 10px;'>فتح الصفحة</a>";
        } else {
            echo "<span style='background: #6c757d; color: white; padding: 10px 20px; border-radius: 5px; display: inline-block; margin-top: 10px;'>غير متاح</span>";
        }
        
        echo "</div>";
    }
    
    echo "</div>";
    
    // اختبار البيانات المطلوبة لصفحات الإضافة
    echo "<h2>اختبار البيانات المطلوبة</h2>";
    
    // فحص المنتجات للمبيعات
    echo "<h3>بيانات المنتجات للمبيعات:</h3>";
    $products = $conn->query("SELECT id, name, selling_price, stock_quantity, unit FROM products WHERE is_active = 1 ORDER BY name LIMIT 5");
    
    if ($products && $products->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>اسم المنتج</th><th>السعر</th><th>المخزون</th><th>الوحدة</th></tr>";
        
        while ($product = $products->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $product['id'] . "</td>";
            echo "<td>" . $product['name'] . "</td>";
            echo "<td>" . number_format($product['selling_price'], 2) . " ريال</td>";
            echo "<td>" . $product['stock_quantity'] . "</td>";
            echo "<td>" . $product['unit'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p style='color: green;'>✅ بيانات المنتجات متاحة للمبيعات</p>";
    } else {
        echo "<p style='color: red;'>❌ لا توجد منتجات متاحة للمبيعات</p>";
    }
    
    // فحص العملاء
    echo "<h3>بيانات العملاء:</h3>";
    $customers = $conn->query("SELECT id, name, phone, balance FROM customers WHERE is_active = 1 ORDER BY name LIMIT 5");
    
    if ($customers && $customers->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>اسم العميل</th><th>الهاتف</th><th>الرصيد</th></tr>";
        
        while ($customer = $customers->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $customer['id'] . "</td>";
            echo "<td>" . $customer['name'] . "</td>";
            echo "<td>" . ($customer['phone'] ?? '-') . "</td>";
            echo "<td>" . number_format($customer['balance'], 2) . " ريال</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p style='color: green;'>✅ بيانات العملاء متاحة</p>";
    } else {
        echo "<p style='color: red;'>❌ لا توجد عملاء متاحين</p>";
    }
    
    // فحص الموردين
    echo "<h3>بيانات الموردين:</h3>";
    $suppliers = $conn->query("SELECT id, name, company, phone, balance FROM suppliers WHERE is_active = 1 ORDER BY name LIMIT 5");
    
    if ($suppliers && $suppliers->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>اسم المورد</th><th>الشركة</th><th>الهاتف</th><th>الرصيد</th></tr>";
        
        while ($supplier = $suppliers->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $supplier['id'] . "</td>";
            echo "<td>" . $supplier['name'] . "</td>";
            echo "<td>" . ($supplier['company'] ?? '-') . "</td>";
            echo "<td>" . ($supplier['phone'] ?? '-') . "</td>";
            echo "<td>" . number_format($supplier['balance'], 2) . " ريال</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p style='color: green;'>✅ بيانات الموردين متاحة</p>";
    } else {
        echo "<p style='color: red;'>❌ لا توجد موردين متاحين</p>";
    }
    
    // فحص التصنيفات
    echo "<h3>تصنيفات المنتجات:</h3>";
    $categories = $conn->query("SELECT id, name, description FROM categories ORDER BY name LIMIT 5");
    
    if ($categories && $categories->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>اسم التصنيف</th><th>الوصف</th></tr>";
        
        while ($category = $categories->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $category['id'] . "</td>";
            echo "<td>" . $category['name'] . "</td>";
            echo "<td>" . ($category['description'] ?? '-') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "<p style='color: green;'>✅ تصنيفات المنتجات متاحة</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد تصنيفات - يمكن إضافة منتجات بدون تصنيف</p>";
    }
    
    // اختبار الأعمدة المطلوبة
    echo "<h2>فحص الأعمدة المطلوبة</h2>";
    
    $required_columns_check = array(
        'products' => array('unit', 'is_active', 'cost_price'),
        'customers' => array('balance', 'notes', 'is_active'),
        'suppliers' => array('balance', 'company', 'notes', 'is_active'),
        'sales' => array('sale_number', 'discount', 'final_amount', 'payment_method'),
        'purchases' => array('purchase_number', 'discount', 'final_amount', 'payment_method'),
        'expenses' => array('expense_type', 'notes', 'payment_method')
    );
    
    $all_columns_ok = true;
    
    foreach ($required_columns_check as $table => $columns) {
        echo "<h4>جدول $table:</h4>";
        foreach ($columns as $column) {
            $result = $conn->query("SHOW COLUMNS FROM $table LIKE '$column'");
            if ($result && $result->num_rows > 0) {
                echo "<span style='color: green;'>✅ $column</span> ";
            } else {
                echo "<span style='color: red;'>❌ $column</span> ";
                $all_columns_ok = false;
            }
        }
        echo "<br><br>";
    }
    
    echo "<hr>";
    echo "<h2>ملخص الاختبار</h2>";
    
    if ($all_columns_ok) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h3 style='color: #155724;'>🎉 جميع صفحات الإضافة جاهزة للاستخدام!</h3>";
        echo "<p>جميع الأعمدة المطلوبة موجودة والبيانات متاحة</p>";
        echo "</div>";
        
        echo "<h3>يمكنك الآن:</h3>";
        echo "<ul>";
        echo "<li>إضافة فواتير مبيعات ومشتريات جديدة</li>";
        echo "<li>إضافة منتجات وعملاء وموردين جدد</li>";
        echo "<li>تسجيل مصروفات جديدة</li>";
        echo "<li>إدارة جميع جوانب النظام</li>";
        echo "</ul>";
        
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h3 style='color: #721c24;'>⚠️ بعض الأعمدة مفقودة</h3>";
        echo "<p>يرجى تشغيل ملف إصلاح الأعمدة المفقودة</p>";
        echo "<p><a href='fix-all-missing-columns.php'>إصلاح الأعمدة المفقودة</a></p>";
        echo "</div>";
    }
    
    echo "<h3>روابط سريعة للاختبار:</h3>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 20px 0;'>";
    
    $quick_test_links = array(
        array('إضافة مبيعات', 'pages/sales/add.php', '#28a745'),
        array('إضافة مشتريات', 'pages/purchases/add.php', '#17a2b8'),
        array('إضافة منتج', 'pages/products/add.php', '#ffc107'),
        array('إضافة عميل', 'pages/customers/add.php', '#6f42c1'),
        array('إضافة مورد', 'pages/suppliers/add.php', '#fd7e14'),
        array('إضافة مصروف', 'pages/expenses/add.php', '#e83e8c')
    );
    
    foreach ($quick_test_links as $link) {
        echo "<div style='text-align: center;'>";
        echo "<a href='{$link[1]}' target='_blank' style='background: {$link[2]}; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; display: inline-block; width: 100%;'>{$link[0]}</a>";
        echo "</div>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<?php
/**
 * ملف إصلاح دوال الخزينة
 * Treasury Functions Fix
 */

echo "<h1>إصلاح دوال الخزينة - Treasury Functions Fix</h1>";

$db_host = "localhost";
$db_user = "root";
$db_pass = "";
$db_name = "zero";

try {
    // الاتصال بقاعدة البيانات
    $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
    $conn->set_charset("utf8mb4");
    
    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }
    
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // التحقق من وجود جدول treasury_transactions
    $result = $conn->query("SHOW TABLES LIKE 'treasury_transactions'");
    
    if ($result && $result->num_rows > 0) {
        echo "<p style='color: green;'>✅ جدول treasury_transactions موجود</p>";
        
        // إضافة بعض البيانات التجريبية للخزينة
        echo "<h3>إضافة بيانات تجريبية للخزينة:</h3>";
        
        // التحقق من وجود بيانات
        $count_result = $conn->query("SELECT COUNT(*) as count FROM treasury_transactions");
        $count = 0;
        if ($count_result) {
            $count_row = $count_result->fetch_assoc();
            $count = $count_row['count'];
        }
        
        if ($count == 0) {
            // إضافة رصيد افتتاحي
            $opening_balance = 10000.00;
            $insert_opening = "INSERT INTO treasury_transactions 
                              (type, amount, description, reference_type, reference_id, user_id, transaction_date) 
                              VALUES 
                              ('income', $opening_balance, 'رصيد افتتاحي', 'opening', NULL, 1, CURDATE())";
            
            if ($conn->query($insert_opening) === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة رصيد افتتاحي: " . number_format($opening_balance, 2) . " ريال</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ خطأ في إضافة الرصيد الافتتاحي: " . $conn->error . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ يوجد $count حركة مالية في الخزينة</p>";
        }
        
        // حساب الرصيد الحالي
        $balance_query = "SELECT 
                            SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as total_income,
                            SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as total_expense
                          FROM treasury_transactions";
        
        $balance_result = $conn->query($balance_query);
        
        if ($balance_result && $balance_result->num_rows > 0) {
            $balance_row = $balance_result->fetch_assoc();
            $total_income = (float) ($balance_row['total_income'] ?? 0);
            $total_expense = (float) ($balance_row['total_expense'] ?? 0);
            $current_balance = $total_income - $total_expense;
            
            echo "<h3>تقرير الخزينة:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>إجمالي الإيرادات</th>";
            echo "<th>إجمالي المصروفات</th>";
            echo "<th>الرصيد الحالي</th>";
            echo "</tr>";
            echo "<tr>";
            echo "<td style='color: green; text-align: center;'>" . number_format($total_income, 2) . " ريال</td>";
            echo "<td style='color: red; text-align: center;'>" . number_format($total_expense, 2) . " ريال</td>";
            echo "<td style='color: blue; text-align: center; font-weight: bold;'>" . number_format($current_balance, 2) . " ريال</td>";
            echo "</tr>";
            echo "</table>";
        }
        
        // عرض آخر الحركات
        echo "<h3>آخر الحركات المالية:</h3>";
        $recent_query = "SELECT * FROM treasury_transactions ORDER BY id DESC LIMIT 5";
        $recent_result = $conn->query($recent_query);
        
        if ($recent_result && $recent_result->num_rows > 0) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>النوع</th>";
            echo "<th>المبلغ</th>";
            echo "<th>الوصف</th>";
            echo "<th>التاريخ</th>";
            echo "</tr>";
            
            while ($transaction = $recent_result->fetch_assoc()) {
                $type_color = $transaction['type'] == 'income' ? 'green' : 'red';
                $type_text = $transaction['type'] == 'income' ? 'إيراد' : 'مصروف';
                
                echo "<tr>";
                echo "<td style='color: $type_color; text-align: center;'>$type_text</td>";
                echo "<td style='text-align: center;'>" . number_format($transaction['amount'], 2) . " ريال</td>";
                echo "<td>" . $transaction['description'] . "</td>";
                echo "<td style='text-align: center;'>" . $transaction['transaction_date'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>لا توجد حركات مالية</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ جدول treasury_transactions غير موجود</p>";
        echo "<p><a href='create-tables.php'>إنشاء الجداول المفقودة</a></p>";
    }
    
    $conn->close();
    
    echo "<hr>";
    echo "<h3 style='color: green;'>🎉 تم إصلاح دوال الخزينة بنجاح!</h3>";
    echo "<p>يمكنك الآن استخدام النظام:</p>";
    echo "<ul>";
    echo "<li><a href='index.php' style='color: blue;'>🏠 الصفحة الرئيسية</a></li>";
    echo "<li><a href='login.php' style='color: blue;'>🔐 تسجيل الدخول</a></li>";
    echo "<li><a href='test-connection.php' style='color: blue;'>🧪 اختبار النظام</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    
    echo "<h3>خطوات استكشاف الأخطاء:</h3>";
    echo "<ol>";
    echo "<li>تأكد من إنشاء جميع الجداول: <a href='create-tables.php'>إنشاء الجداول</a></li>";
    echo "<li>تحقق من حالة قاعدة البيانات: <a href='database-status.php'>فحص قاعدة البيانات</a></li>";
    echo "<li>اختبر الاتصال: <a href='test-connection.php'>اختبار الاتصال</a></li>";
    echo "</ol>";
}

echo "<hr>";
echo "<h3>ملاحظات مهمة:</h3>";
echo "<ul>";
echo "<li>تم تحديث دوال الخزينة لتعمل مع جدول treasury_transactions</li>";
echo "<li>تم إصلاح دالة getTreasuryBalance() لحساب الرصيد من الإيرادات والمصروفات</li>";
echo "<li>تم إصلاح دالة addTreasuryTransaction() لإضافة الحركات بالتنسيق الصحيح</li>";
echo "<li>تم إصلاح دالة deleteTreasuryTransaction() للحذف من الجدول الصحيح</li>";
echo "</ul>";
?>

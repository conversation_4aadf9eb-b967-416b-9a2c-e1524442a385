<?php
/**
 * ملف إعادة تعيين قاعدة البيانات
 * Database Reset Script
 */

echo "<h1>إعادة تعيين قاعدة البيانات - Database Reset</h1>";

// التحقق من التأكيد
if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #856404;'>⚠️ تحذير!</h3>";
    echo "<p>هذا الإجراء سيحذف قاعدة البيانات الحالية وجميع البيانات الموجودة بها!</p>";
    echo "<p>هل أنت متأكد من أنك تريد المتابعة؟</p>";
    echo "<a href='?confirm=yes' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>نعم، احذف قاعدة البيانات</a> ";
    echo "<a href='index.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>إلغاء</a>";
    echo "</div>";
    exit;
}

// معلومات الاتصال
$db_host = "localhost";
$db_user = "root";
$db_pass = "";

try {
    // الاتصال بـ MySQL
    $conn = new mysqli($db_host, $db_user, $db_pass);
    $conn->set_charset("utf8mb4");
    
    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بـ MySQL: " . $conn->connect_error);
    }
    
    echo "<p style='color: green;'>✅ تم الاتصال بـ MySQL بنجاح</p>";
    
    // حذف قاعدة البيانات إذا كانت موجودة
    $sql = "DROP DATABASE IF EXISTS `zero`";
    
    if ($conn->query($sql) === TRUE) {
        echo "<p style='color: orange;'>🗑️ تم حذف قاعدة البيانات القديمة</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ لم توجد قاعدة بيانات للحذف</p>";
    }
    
    // إنشاء قاعدة البيانات الجديدة
    $sql = "CREATE DATABASE `zero` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    
    if ($conn->query($sql) === TRUE) {
        echo "<p style='color: green;'>✅ تم إنشاء قاعدة البيانات الجديدة</p>";
    } else {
        throw new Exception("خطأ في إنشاء قاعدة البيانات: " . $conn->error);
    }
    
    // اختيار قاعدة البيانات
    $conn->select_db("zero");
    
    // قراءة وتنفيذ ملف SQL
    $sql_file = "database/zero.sql";
    
    if (!file_exists($sql_file)) {
        throw new Exception("ملف قاعدة البيانات غير موجود: " . $sql_file);
    }
    
    $sql_content = file_get_contents($sql_file);
    
    if ($sql_content === false) {
        throw new Exception("فشل في قراءة ملف قاعدة البيانات");
    }
    
    // تنفيذ الاستعلامات
    $queries = explode(';', $sql_content);
    $success_count = 0;
    
    foreach ($queries as $query) {
        $query = trim($query);
        
        if (empty($query) || strpos($query, '--') === 0 || strpos($query, '/*') === 0) {
            continue;
        }
        
        if ($conn->query($query) === TRUE) {
            $success_count++;
        } else {
            echo "<p style='color: orange;'>⚠️ خطأ: " . $conn->error . "</p>";
        }
    }
    
    echo "<p style='color: green;'>✅ تم تنفيذ $success_count استعلام بنجاح</p>";
    
    $conn->close();
    
    echo "<hr>";
    echo "<h3 style='color: green;'>🎉 تم إعادة تعيين قاعدة البيانات بنجاح!</h3>";
    echo "<p><a href='login.php' style='color: blue;'>🔐 تسجيل الدخول</a></p>";
    echo "<p><strong>بيانات الدخول:</strong> admin / admin</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

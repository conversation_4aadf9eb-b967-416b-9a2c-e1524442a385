<?php
/**
 * ملف إنشاء الجداول
 * Tables Creation Script
 */

echo "<h1>إنشاء جداول قاعدة البيانات - Creating Database Tables</h1>";

// معلومات الاتصال
$db_host = "localhost";
$db_user = "root";
$db_pass = "";
$db_name = "zero";

try {
    // الاتصال بقاعدة البيانات
    $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
    $conn->set_charset("utf8mb4");
    
    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }
    
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات 'zero' بنجاح</p>";
    
    // إنشاء الجداول مباشرة
    $tables = array();
    
    // جدول المستخدمين
    $tables['users'] = "
    CREATE TABLE IF NOT EXISTS `users` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `username` varchar(50) NOT NULL,
      `password` varchar(255) NOT NULL,
      `name` varchar(100) NOT NULL,
      `role` enum('admin','cashier','manager') NOT NULL DEFAULT 'cashier',
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      UNIQUE KEY `username` (`username`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // جدول التصنيفات
    $tables['categories'] = "
    CREATE TABLE IF NOT EXISTS `categories` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `name` varchar(100) NOT NULL,
      `description` text,
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // جدول المنتجات
    $tables['products'] = "
    CREATE TABLE IF NOT EXISTS `products` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `name` varchar(100) NOT NULL,
      `barcode` varchar(50) DEFAULT NULL,
      `category_id` int(11) DEFAULT NULL,
      `purchase_price` decimal(10,2) NOT NULL DEFAULT '0.00',
      `selling_price` decimal(10,2) NOT NULL DEFAULT '0.00',
      `stock_quantity` int(11) NOT NULL DEFAULT '0',
      `min_stock` int(11) DEFAULT '0',
      `description` text,
      `image` varchar(255) DEFAULT NULL,
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      UNIQUE KEY `barcode` (`barcode`),
      KEY `category_id` (`category_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // جدول العملاء
    $tables['customers'] = "
    CREATE TABLE IF NOT EXISTS `customers` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `name` varchar(100) NOT NULL,
      `phone` varchar(20) DEFAULT NULL,
      `email` varchar(100) DEFAULT NULL,
      `address` text,
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // جدول الموردين
    $tables['suppliers'] = "
    CREATE TABLE IF NOT EXISTS `suppliers` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `name` varchar(100) NOT NULL,
      `phone` varchar(20) DEFAULT NULL,
      `email` varchar(100) DEFAULT NULL,
      `address` text,
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // جدول المبيعات
    $tables['sales'] = "
    CREATE TABLE IF NOT EXISTS `sales` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `sale_number` varchar(50) DEFAULT NULL,
      `customer_id` int(11) DEFAULT NULL,
      `user_id` int(11) NOT NULL,
      `sale_date` date NOT NULL,
      `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
      `discount` decimal(10,2) DEFAULT '0.00',
      `tax` decimal(10,2) DEFAULT '0.00',
      `notes` text,
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `customer_id` (`customer_id`),
      KEY `user_id` (`user_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // جدول تفاصيل المبيعات
    $tables['sale_items'] = "
    CREATE TABLE IF NOT EXISTS `sale_items` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `sale_id` int(11) NOT NULL,
      `product_id` int(11) NOT NULL,
      `quantity` int(11) NOT NULL,
      `unit_price` decimal(10,2) NOT NULL,
      `total_price` decimal(10,2) NOT NULL,
      PRIMARY KEY (`id`),
      KEY `sale_id` (`sale_id`),
      KEY `product_id` (`product_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // جدول المشتريات
    $tables['purchases'] = "
    CREATE TABLE IF NOT EXISTS `purchases` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `purchase_number` varchar(50) DEFAULT NULL,
      `supplier_id` int(11) DEFAULT NULL,
      `user_id` int(11) NOT NULL,
      `purchase_date` date NOT NULL,
      `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
      `notes` text,
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `supplier_id` (`supplier_id`),
      KEY `user_id` (`user_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // جدول تفاصيل المشتريات
    $tables['purchase_items'] = "
    CREATE TABLE IF NOT EXISTS `purchase_items` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `purchase_id` int(11) NOT NULL,
      `product_id` int(11) NOT NULL,
      `quantity` int(11) NOT NULL,
      `unit_price` decimal(10,2) NOT NULL,
      `total_price` decimal(10,2) NOT NULL,
      PRIMARY KEY (`id`),
      KEY `purchase_id` (`purchase_id`),
      KEY `product_id` (`product_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // جدول المصروفات
    $tables['expenses'] = "
    CREATE TABLE IF NOT EXISTS `expenses` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `description` varchar(255) NOT NULL,
      `amount` decimal(10,2) NOT NULL,
      `expense_date` date NOT NULL,
      `category` varchar(100) DEFAULT NULL,
      `user_id` int(11) NOT NULL,
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `user_id` (`user_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // جدول حركات الخزينة
    $tables['treasury_transactions'] = "
    CREATE TABLE IF NOT EXISTS `treasury_transactions` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `type` enum('income','expense') NOT NULL,
      `amount` decimal(10,2) NOT NULL,
      `description` varchar(255) NOT NULL,
      `reference_type` varchar(50) DEFAULT NULL,
      `reference_id` int(11) DEFAULT NULL,
      `user_id` int(11) NOT NULL,
      `transaction_date` date NOT NULL,
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `user_id` (`user_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // جدول الإعدادات
    $tables['settings'] = "
    CREATE TABLE IF NOT EXISTS `settings` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `setting_key` varchar(100) NOT NULL,
      `setting_value` text,
      `description` varchar(255) DEFAULT NULL,
      PRIMARY KEY (`id`),
      UNIQUE KEY `setting_key` (`setting_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // إنشاء الجداول
    $created_count = 0;
    $error_count = 0;
    
    foreach ($tables as $table_name => $sql) {
        if ($conn->query($sql) === TRUE) {
            echo "<p style='color: green;'>✅ تم إنشاء جدول '$table_name' بنجاح</p>";
            $created_count++;
        } else {
            echo "<p style='color: red;'>❌ خطأ في إنشاء جدول '$table_name': " . $conn->error . "</p>";
            $error_count++;
        }
    }
    
    // إدخال المستخدم الافتراضي
    echo "<h3>إنشاء المستخدم الافتراضي:</h3>";
    
    $admin_password = password_hash('admin', PASSWORD_DEFAULT);
    $insert_admin = "INSERT INTO `users` (`username`, `password`, `name`, `role`) VALUES 
                     ('admin', '$admin_password', 'مدير النظام', 'admin')
                     ON DUPLICATE KEY UPDATE password = '$admin_password'";
    
    if ($conn->query($insert_admin) === TRUE) {
        echo "<p style='color: green;'>✅ تم إنشاء المستخدم الافتراضي بنجاح</p>";
    } else {
        echo "<p style='color: red;'>❌ خطأ في إنشاء المستخدم الافتراضي: " . $conn->error . "</p>";
    }
    
    // إدخال بعض الإعدادات الافتراضية
    $default_settings = array(
        array('company_name', 'شركة Zero', 'اسم الشركة'),
        array('company_phone', '', 'هاتف الشركة'),
        array('company_address', '', 'عنوان الشركة'),
        array('currency', 'ريال', 'العملة المستخدمة'),
        array('tax_rate', '15', 'معدل الضريبة'),
        array('backup_enabled', '1', 'تفعيل النسخ الاحتياطي')
    );
    
    echo "<h3>إنشاء الإعدادات الافتراضية:</h3>";
    
    foreach ($default_settings as $setting) {
        $key = $setting[0];
        $value = $setting[1];
        $desc = $setting[2];
        
        $insert_setting = "INSERT INTO `settings` (`setting_key`, `setting_value`, `description`) VALUES 
                          ('$key', '$value', '$desc')
                          ON DUPLICATE KEY UPDATE setting_value = '$value'";
        
        if ($conn->query($insert_setting) === TRUE) {
            echo "<p style='color: green;'>✅ تم إنشاء إعداد '$desc'</p>";
        }
    }
    
    $conn->close();
    
    echo "<hr>";
    echo "<h3 style='color: green;'>🎉 تم إنشاء جميع الجداول بنجاح!</h3>";
    echo "<p>تم إنشاء $created_count جدول</p>";
    
    if ($error_count > 0) {
        echo "<p style='color: orange;'>⚠️ فشل في إنشاء $error_count جدول</p>";
    }
    
    echo "<h4>يمكنك الآن استخدام النظام:</h4>";
    echo "<ul>";
    echo "<li><a href='login.php' style='color: blue;'>🔐 تسجيل الدخول</a></li>";
    echo "<li><a href='test-connection.php' style='color: blue;'>🧪 اختبار النظام</a></li>";
    echo "</ul>";
    
    echo "<h4>بيانات الدخول:</h4>";
    echo "<ul>";
    echo "<li><strong>اسم المستخدم:</strong> admin</li>";
    echo "<li><strong>كلمة المرور:</strong> admin</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

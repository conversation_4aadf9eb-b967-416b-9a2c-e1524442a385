<?php
/**
 * ملف إصلاح أعمدة المبيعات المفقودة
 * Fix Missing Sales Columns Script
 */

echo "<h1>إصلاح أعمدة المبيعات المفقودة - Fix Missing Sales Columns</h1>";

$db_host = "localhost";
$db_user = "root";
$db_pass = "";
$db_name = "zero";

try {
    // الاتصال بقاعدة البيانات
    $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
    $conn->set_charset("utf8mb4");
    
    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }
    
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // إضافة الأعمدة المفقودة لجدول المبيعات
    echo "<h2>إضافة الأعمدة المفقودة لجدول المبيعات</h2>";
    
    $sales_columns = array(
        'sale_number' => "ADD COLUMN sale_number VARCHAR(50) DEFAULT NULL AFTER id",
        'discount' => "ADD COLUMN discount DECIMAL(10,2) DEFAULT 0.00 AFTER total_amount",
        'discount_amount' => "ADD COLUMN discount_amount DECIMAL(10,2) DEFAULT 0.00 AFTER discount",
        'tax_amount' => "ADD COLUMN tax_amount DECIMAL(10,2) DEFAULT 0.00 AFTER discount_amount",
        'payment_method' => "ADD COLUMN payment_method ENUM('cash','card','bank_transfer','check','credit') DEFAULT 'cash' AFTER payment_status",
        'notes' => "ADD COLUMN notes TEXT DEFAULT NULL AFTER payment_method"
    );
    
    $fixes_applied = 0;
    
    foreach ($sales_columns as $column => $sql) {
        $check = $conn->query("SHOW COLUMNS FROM sales LIKE '$column'");
        if (!$check || $check->num_rows == 0) {
            if ($conn->query("ALTER TABLE sales $sql") === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column' لجدول sales</p>";
                $fixes_applied++;
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود '$column': " . $conn->error . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column' موجود بالفعل في جدول sales</p>";
        }
    }
    
    // تحديث البيانات الموجودة
    echo "<h2>تحديث البيانات الموجودة</h2>";
    
    // إنشاء أرقام مبيعات للفواتير الموجودة
    $update_sale_numbers = "UPDATE sales SET sale_number = CONCAT('S', LPAD(id, 6, '0')) WHERE sale_number IS NULL OR sale_number = ''";
    if ($conn->query($update_sale_numbers) === TRUE) {
        echo "<p style='color: green;'>✅ تم تحديث أرقام المبيعات</p>";
    }
    
    // تحديث طريقة الدفع الافتراضية
    $update_payment_method = "UPDATE sales SET payment_method = 'cash' WHERE payment_method IS NULL OR payment_method = ''";
    if ($conn->query($update_payment_method) === TRUE) {
        echo "<p style='color: green;'>✅ تم تحديث طرق الدفع</p>";
    }
    
    // تحديث الخصومات
    $update_discounts = "UPDATE sales SET discount = 0.00, discount_amount = 0.00, tax_amount = 0.00 WHERE discount IS NULL";
    if ($conn->query($update_discounts) === TRUE) {
        echo "<p style='color: green;'>✅ تم تحديث الخصومات والضرائب</p>";
    }
    
    // إضافة الأعمدة المفقودة لجدول المشتريات أيضاً
    echo "<h2>إضافة الأعمدة المفقودة لجدول المشتريات</h2>";
    
    $purchases_columns = array(
        'purchase_number' => "ADD COLUMN purchase_number VARCHAR(50) DEFAULT NULL AFTER id",
        'discount' => "ADD COLUMN discount DECIMAL(10,2) DEFAULT 0.00 AFTER total_amount",
        'discount_amount' => "ADD COLUMN discount_amount DECIMAL(10,2) DEFAULT 0.00 AFTER discount",
        'tax_amount' => "ADD COLUMN tax_amount DECIMAL(10,2) DEFAULT 0.00 AFTER discount_amount",
        'payment_method' => "ADD COLUMN payment_method ENUM('cash','card','bank_transfer','check','credit') DEFAULT 'cash' AFTER payment_status"
    );
    
    foreach ($purchases_columns as $column => $sql) {
        $check = $conn->query("SHOW COLUMNS FROM purchases LIKE '$column'");
        if (!$check || $check->num_rows == 0) {
            if ($conn->query("ALTER TABLE purchases $sql") === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column' لجدول purchases</p>";
                $fixes_applied++;
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود '$column': " . $conn->error . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column' موجود بالفعل في جدول purchases</p>";
        }
    }
    
    // تحديث بيانات المشتريات
    $update_purchase_numbers = "UPDATE purchases SET purchase_number = CONCAT('P', LPAD(id, 6, '0')) WHERE purchase_number IS NULL OR purchase_number = ''";
    if ($conn->query($update_purchase_numbers) === TRUE) {
        echo "<p style='color: green;'>✅ تم تحديث أرقام المشتريات</p>";
    }
    
    $update_purchase_payment = "UPDATE purchases SET payment_method = 'cash' WHERE payment_method IS NULL OR payment_method = ''";
    if ($conn->query($update_purchase_payment) === TRUE) {
        echo "<p style='color: green;'>✅ تم تحديث طرق دفع المشتريات</p>";
    }
    
    $update_purchase_discounts = "UPDATE purchases SET discount = 0.00, discount_amount = 0.00, tax_amount = 0.00 WHERE discount IS NULL";
    if ($conn->query($update_purchase_discounts) === TRUE) {
        echo "<p style='color: green;'>✅ تم تحديث خصومات وضرائب المشتريات</p>";
    }
    
    // عرض بنية الجداول المحدثة
    echo "<h2>بنية جدول المبيعات المحدثة</h2>";
    $sales_structure = $conn->query("DESCRIBE sales");
    if ($sales_structure) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>اسم العمود</th><th>النوع</th><th>القيمة الافتراضية</th></tr>";
        while ($column = $sales_structure->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // عرض عينة من البيانات المحدثة
    echo "<h2>عينة من بيانات المبيعات المحدثة</h2>";
    $sample_sales = $conn->query("SELECT id, sale_number, sale_date, total_amount, discount, discount_amount, final_amount, paid_amount, payment_method, payment_status FROM sales LIMIT 5");
    
    if ($sample_sales && $sample_sales->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>رقم المبيعة</th><th>التاريخ</th><th>المبلغ الإجمالي</th><th>الخصم</th><th>مبلغ الخصم</th><th>المبلغ النهائي</th><th>المدفوع</th><th>طريقة الدفع</th><th>حالة الدفع</th>";
        echo "</tr>";
        
        while ($sale = $sample_sales->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $sale['id'] . "</td>";
            echo "<td>" . $sale['sale_number'] . "</td>";
            echo "<td>" . $sale['sale_date'] . "</td>";
            echo "<td>" . number_format($sale['total_amount'], 2) . "</td>";
            echo "<td>" . number_format($sale['discount'], 2) . "</td>";
            echo "<td>" . number_format($sale['discount_amount'], 2) . "</td>";
            echo "<td>" . number_format($sale['final_amount'], 2) . "</td>";
            echo "<td>" . number_format($sale['paid_amount'], 2) . "</td>";
            echo "<td>" . $sale['payment_method'] . "</td>";
            echo "<td>" . $sale['payment_status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    $conn->close();
    
    echo "<hr>";
    echo "<h2>تم الانتهاء من الإصلاحات</h2>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>🎉 تم إصلاح جميع أعمدة المبيعات والمشتريات!</h3>";
    echo "<p>تم تطبيق $fixes_applied إصلاح</p>";
    echo "</div>";
    
    echo "<h3>يمكنك الآن استخدام صفحات المبيعات والمشتريات بدون أخطاء:</h3>";
    echo "<ul>";
    echo "<li><a href='pages/sales/index.php' style='color: blue;'>💰 إدارة المبيعات</a></li>";
    echo "<li><a href='pages/purchases/index.php' style='color: blue;'>🛒 إدارة المشتريات</a></li>";
    echo "<li><a href='pages/sales/add.php' style='color: blue;'>➕ إضافة فاتورة مبيعات</a></li>";
    echo "<li><a href='pages/purchases/add.php' style='color: blue;'>➕ إضافة فاتورة مشتريات</a></li>";
    echo "<li><a href='index.php' style='color: blue;'>🏠 الصفحة الرئيسية</a></li>";
    echo "</ul>";
    
    echo "<h3>الأعمدة المضافة:</h3>";
    echo "<ul>";
    echo "<li><strong>sale_number / purchase_number:</strong> رقم الفاتورة</li>";
    echo "<li><strong>discount:</strong> نسبة الخصم</li>";
    echo "<li><strong>discount_amount:</strong> مبلغ الخصم</li>";
    echo "<li><strong>tax_amount:</strong> مبلغ الضريبة</li>";
    echo "<li><strong>payment_method:</strong> طريقة الدفع</li>";
    echo "<li><strong>notes:</strong> ملاحظات (للمبيعات)</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

# دليل الإعداد السريع - Quick Setup Guide

## المشكلة الحالية / Current Issue
```
Fatal error: Unknown database 'zero'
```

## الحل / Solution

### الخطوة 1: تأكد من تشغيل WAMP
1. افتح WAMP
2. تأكد من أن الأيقونة خضراء اللون
3. تأكد من تشغيل Apache و MySQL

### الخطوة 2: إنشاء قاعدة البيانات
افتح أحد الروابط التالية في المتصفح:

**الطريقة الأولى - ملف الإنشاء التلقائي:**
```
http://localhost/zero4/create-database.php
```

**الطريقة الثانية - phpMyAdmin:**
```
http://localhost/phpmyadmin
```
ثم:
1. انقر على "New" لإنشاء قاعدة بيانات جديدة
2. اكت<PERSON> اسم قاعدة البيانات: `zero`
3. اختر Collation: `utf8mb4_unicode_ci`
4. انقر "Create"
5. اذهب إلى تبويب "Import"
6. اختر ملف `database/zero.sql`
7. انقر "Go"

### الخطوة 3: التحقق من النظام
```
http://localhost/zero4/test-connection.php
```

### الخطوة 4: تسجيل الدخول
```
http://localhost/zero4/login.php
```

**بيانات الدخول:**
- اسم المستخدم: `admin`
- كلمة المرور: `admin`

## ملفات مساعدة إضافية / Additional Helper Files

### إعادة تعيين قاعدة البيانات (في حالة وجود مشاكل)
```
http://localhost/zero4/reset-database.php
```
⚠️ **تحذير:** هذا سيحذف جميع البيانات الموجودة

### اختبار الاتصال
```
http://localhost/zero4/test-connection.php
```

## استكشاف الأخطاء / Troubleshooting

### إذا لم يعمل الرابط:
1. تأكد من أن WAMP يعمل
2. تأكد من أن المجلد في `C:\wamp64\www\zero4`
3. جرب: `http://localhost:80/zero4`

### إذا ظهر خطأ في قاعدة البيانات:
1. تأكد من تشغيل MySQL في WAMP
2. تحقق من بيانات الاتصال في `config/db_config.php`
3. استخدم `reset-database.php` لإعادة الإنشاء

### إذا ظهر خطأ في الصلاحيات:
1. تأكد من أن مجلد `uploads` قابل للكتابة
2. تأكد من صلاحيات المجلدات

## الخطوات التالية / Next Steps

بعد حل المشكلة:
1. سجل الدخول بالبيانات الافتراضية
2. غير كلمة المرور من الإعدادات
3. أضف المنتجات والعملاء
4. ابدأ في استخدام النظام

## روابط مفيدة / Useful Links

- **الصفحة الرئيسية:** `http://localhost/zero4/`
- **تسجيل الدخول:** `http://localhost/zero4/login.php`
- **اختبار النظام:** `http://localhost/zero4/test-connection.php`
- **إنشاء قاعدة البيانات:** `http://localhost/zero4/create-database.php`
- **phpMyAdmin:** `http://localhost/phpmyadmin`

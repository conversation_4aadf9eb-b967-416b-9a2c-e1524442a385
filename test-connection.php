<?php
/**
 * ملف اختبار الاتصال بقاعدة البيانات والتطبيق
 * Test file for database connection and application
 */

echo "<h1>اختبار نظام Zero</h1>";
echo "<h2>Zero System Test</h2>";

// اختبار إعدادات PHP
echo "<h3>1. إعدادات PHP</h3>";
echo "إصدار PHP: " . phpversion() . "<br>";
echo "المنطقة الزمنية: " . date_default_timezone_get() . "<br>";
echo "الوقت الحالي: " . date('Y-m-d H:i:s') . "<br>";

// اختبار الاتصال بقاعدة البيانات
echo "<h3>2. اختبار قاعدة البيانات</h3>";

try {
    require_once "config/db_config.php";
    
    if ($conn->connect_error) {
        echo "<span style='color: red;'>❌ فشل الاتصال بقاعدة البيانات: " . $conn->connect_error . "</span><br>";
    } else {
        echo "<span style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</span><br>";
        
        // اختبار الجداول
        $tables = ['users', 'products', 'categories', 'customers', 'suppliers', 'sales', 'purchases'];
        echo "<h4>الجداول المتاحة:</h4>";
        
        foreach ($tables as $table) {
            $result = $conn->query("SHOW TABLES LIKE '$table'");
            if ($result && $result->num_rows > 0) {
                echo "<span style='color: green;'>✅ جدول $table موجود</span><br>";
            } else {
                echo "<span style='color: red;'>❌ جدول $table غير موجود</span><br>";
            }
        }
        
        // اختبار بيانات المستخدمين
        echo "<h4>بيانات المستخدمين:</h4>";
        $result = $conn->query("SELECT COUNT(*) as count FROM users");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "عدد المستخدمين: " . $row['count'] . "<br>";
        }
    }
} catch (Exception $e) {
    echo "<span style='color: red;'>❌ خطأ: " . $e->getMessage() . "</span><br>";
}

// اختبار الملفات المطلوبة
echo "<h3>3. اختبار الملفات</h3>";
$required_files = [
    'config/config.php',
    'config/database.php', 
    'includes/functions.php',
    'includes/header.php',
    'includes/footer.php',
    'login.php'
];

foreach ($required_files as $file) {
    if (file_exists($file)) {
        echo "<span style='color: green;'>✅ $file موجود</span><br>";
    } else {
        echo "<span style='color: red;'>❌ $file غير موجود</span><br>";
    }
}

// اختبار مجلدات الرفع
echo "<h3>4. اختبار مجلدات الرفع</h3>";
$upload_dirs = ['uploads', 'uploads/logo', 'uploads/products'];

foreach ($upload_dirs as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<span style='color: green;'>✅ مجلد $dir موجود وقابل للكتابة</span><br>";
        } else {
            echo "<span style='color: orange;'>⚠️ مجلد $dir موجود لكن غير قابل للكتابة</span><br>";
        }
    } else {
        echo "<span style='color: red;'>❌ مجلد $dir غير موجود</span><br>";
    }
}

echo "<h3>5. روابط التطبيق</h3>";
echo "<a href='index.php' style='color: blue;'>🏠 الصفحة الرئيسية</a><br>";
echo "<a href='login.php' style='color: blue;'>🔐 صفحة تسجيل الدخول</a><br>";

echo "<hr>";
echo "<p><strong>ملاحظة:</strong> إذا كانت جميع الاختبارات ناجحة، يمكنك الآن استخدام النظام.</p>";
echo "<p><strong>بيانات الدخول الافتراضية:</strong></p>";
echo "<ul>";
echo "<li>اسم المستخدم: admin</li>";
echo "<li>كلمة المرور: admin</li>";
echo "</ul>";
?>

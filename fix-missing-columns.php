<?php
/**
 * ملف إصلاح الأعمدة المفقودة
 * Fix Missing Columns Script
 */

echo "<h1>إصلاح الأعمدة المفقودة - Fix Missing Columns</h1>";

$db_host = "localhost";
$db_user = "root";
$db_pass = "";
$db_name = "zero";

try {
    // الاتصال بقاعدة البيانات
    $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
    $conn->set_charset("utf8mb4");
    
    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }
    
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    $fixes_applied = 0;
    $errors_count = 0;
    
    // 1. إصلاح جدول المبيعات (sales)
    echo "<h2>1. إصلاح جدول المبيعات</h2>";
    
    $sales_columns = array(
        'final_amount' => "ADD COLUMN final_amount DECIMAL(10,2) DEFAULT 0.00 AFTER total_amount",
        'paid_amount' => "ADD COLUMN paid_amount DECIMAL(10,2) DEFAULT 0.00 AFTER final_amount",
        'payment_status' => "ADD COLUMN payment_status ENUM('paid','partial','unpaid') DEFAULT 'unpaid' AFTER paid_amount"
    );
    
    foreach ($sales_columns as $column => $sql) {
        $check = $conn->query("SHOW COLUMNS FROM sales LIKE '$column'");
        if (!$check || $check->num_rows == 0) {
            if ($conn->query("ALTER TABLE sales $sql") === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column' لجدول sales</p>";
                $fixes_applied++;
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود '$column': " . $conn->error . "</p>";
                $errors_count++;
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column' موجود بالفعل في جدول sales</p>";
        }
    }
    
    // تحديث البيانات الموجودة في جدول المبيعات
    $conn->query("UPDATE sales SET final_amount = total_amount WHERE final_amount = 0");
    $conn->query("UPDATE sales SET paid_amount = total_amount WHERE paid_amount = 0");
    $conn->query("UPDATE sales SET payment_status = 'paid' WHERE payment_status = 'unpaid'");
    
    // 2. إصلاح جدول المشتريات (purchases)
    echo "<h2>2. إصلاح جدول المشتريات</h2>";
    
    $purchases_columns = array(
        'final_amount' => "ADD COLUMN final_amount DECIMAL(10,2) DEFAULT 0.00 AFTER total_amount",
        'paid_amount' => "ADD COLUMN paid_amount DECIMAL(10,2) DEFAULT 0.00 AFTER final_amount",
        'payment_status' => "ADD COLUMN payment_status ENUM('paid','partial','unpaid') DEFAULT 'unpaid' AFTER paid_amount"
    );
    
    foreach ($purchases_columns as $column => $sql) {
        $check = $conn->query("SHOW COLUMNS FROM purchases LIKE '$column'");
        if (!$check || $check->num_rows == 0) {
            if ($conn->query("ALTER TABLE purchases $sql") === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column' لجدول purchases</p>";
                $fixes_applied++;
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود '$column': " . $conn->error . "</p>";
                $errors_count++;
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column' موجود بالفعل في جدول purchases</p>";
        }
    }
    
    // تحديث البيانات الموجودة في جدول المشتريات
    $conn->query("UPDATE purchases SET final_amount = total_amount WHERE final_amount = 0");
    $conn->query("UPDATE purchases SET paid_amount = total_amount WHERE paid_amount = 0");
    $conn->query("UPDATE purchases SET payment_status = 'paid' WHERE payment_status = 'unpaid'");
    
    // 3. إصلاح جدول المنتجات (products)
    echo "<h2>3. إصلاح جدول المنتجات</h2>";
    
    $products_columns = array(
        'is_active' => "ADD COLUMN is_active TINYINT(1) DEFAULT 1 AFTER description",
        'cost_price' => "ADD COLUMN cost_price DECIMAL(10,2) DEFAULT 0.00 AFTER purchase_price",
        'profit_margin' => "ADD COLUMN profit_margin DECIMAL(5,2) DEFAULT 0.00 AFTER cost_price"
    );
    
    foreach ($products_columns as $column => $sql) {
        $check = $conn->query("SHOW COLUMNS FROM products LIKE '$column'");
        if (!$check || $check->num_rows == 0) {
            if ($conn->query("ALTER TABLE products $sql") === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column' لجدول products</p>";
                $fixes_applied++;
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود '$column': " . $conn->error . "</p>";
                $errors_count++;
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column' موجود بالفعل في جدول products</p>";
        }
    }
    
    // تحديث البيانات الموجودة في جدول المنتجات
    $conn->query("UPDATE products SET cost_price = purchase_price WHERE cost_price = 0");
    $conn->query("UPDATE products SET is_active = 1 WHERE is_active IS NULL");
    
    // 4. إصلاح جدول المصروفات (expenses)
    echo "<h2>4. إصلاح جدول المصروفات</h2>";
    
    $expenses_columns = array(
        'expense_type' => "ADD COLUMN expense_type VARCHAR(100) DEFAULT 'عام' AFTER category",
        'receipt_number' => "ADD COLUMN receipt_number VARCHAR(50) DEFAULT NULL AFTER expense_type",
        'notes' => "ADD COLUMN notes TEXT DEFAULT NULL AFTER receipt_number"
    );
    
    foreach ($expenses_columns as $column => $sql) {
        $check = $conn->query("SHOW COLUMNS FROM expenses LIKE '$column'");
        if (!$check || $check->num_rows == 0) {
            if ($conn->query("ALTER TABLE expenses $sql") === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column' لجدول expenses</p>";
                $fixes_applied++;
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة عمود '$column': " . $conn->error . "</p>";
                $errors_count++;
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column' موجود بالفعل في جدول expenses</p>";
        }
    }
    
    // تحديث البيانات الموجودة في جدول المصروفات
    $conn->query("UPDATE expenses SET expense_type = category WHERE expense_type = 'عام' OR expense_type IS NULL");
    
    // 5. إنشاء جدول treasury إذا لم يكن موجوداً (للتوافق مع الصفحات القديمة)
    echo "<h2>5. إنشاء جدول treasury للتوافق</h2>";
    
    $check_treasury = $conn->query("SHOW TABLES LIKE 'treasury'");
    if (!$check_treasury || $check_treasury->num_rows == 0) {
        $create_treasury = "CREATE TABLE treasury (
            id INT(11) NOT NULL AUTO_INCREMENT,
            transaction_type VARCHAR(50) NOT NULL,
            reference_id INT(11) DEFAULT NULL,
            amount DECIMAL(10,2) NOT NULL,
            balance_after DECIMAL(10,2) NOT NULL,
            description VARCHAR(255) NOT NULL,
            user_id INT(11) NOT NULL,
            transaction_date DATE NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($create_treasury) === TRUE) {
            echo "<p style='color: green;'>✅ تم إنشاء جدول treasury للتوافق</p>";
            $fixes_applied++;
            
            // نسخ البيانات من treasury_transactions إلى treasury
            $copy_data = "INSERT INTO treasury (transaction_type, reference_id, amount, balance_after, description, user_id, transaction_date)
                         SELECT reference_type, reference_id, amount, 
                                (SELECT SUM(CASE WHEN type = 'income' THEN amount ELSE -amount END) 
                                 FROM treasury_transactions t2 WHERE t2.id <= t1.id) as balance_after,
                                description, user_id, transaction_date
                         FROM treasury_transactions t1
                         ORDER BY id";
            
            if ($conn->query($copy_data) === TRUE) {
                echo "<p style='color: green;'>✅ تم نسخ البيانات من treasury_transactions إلى treasury</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ خطأ في إنشاء جدول treasury: " . $conn->error . "</p>";
            $errors_count++;
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ جدول treasury موجود بالفعل</p>";
    }
    
    // 6. إضافة بيانات تجريبية للمبيعات والمشتريات
    echo "<h2>6. إضافة بيانات تجريبية</h2>";
    
    // التحقق من وجود مبيعات
    $sales_count = $conn->query("SELECT COUNT(*) as count FROM sales")->fetch_assoc()['count'];
    if ($sales_count == 0) {
        // إضافة مبيعات تجريبية
        $sample_sales = array(
            array(1, 1, '2024-01-15', 1500.00, 1500.00, 1500.00, 'paid'),
            array(2, 1, '2024-01-16', 2300.00, 2300.00, 2000.00, 'partial'),
            array(null, 1, '2024-01-17', 800.00, 800.00, 800.00, 'paid'),
            array(3, 1, '2024-01-18', 3200.00, 3200.00, 0.00, 'unpaid')
        );
        
        foreach ($sample_sales as $sale) {
            $insert_sale = "INSERT INTO sales (customer_id, user_id, sale_date, total_amount, final_amount, paid_amount, payment_status) 
                           VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insert_sale);
            $stmt->bind_param("iisddds", $sale[0], $sale[1], $sale[2], $sale[3], $sale[4], $sale[5], $sale[6]);
            
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✅ تم إضافة فاتورة مبيعات بمبلغ " . number_format($sale[3], 2) . " ريال</p>";
            }
        }
    }
    
    // التحقق من وجود مشتريات
    $purchases_count = $conn->query("SELECT COUNT(*) as count FROM purchases")->fetch_assoc()['count'];
    if ($purchases_count == 0) {
        // إضافة مشتريات تجريبية
        $sample_purchases = array(
            array(1, 1, '2024-01-10', 5000.00, 5000.00, 5000.00, 'paid'),
            array(2, 1, '2024-01-12', 3500.00, 3500.00, 2000.00, 'partial'),
            array(3, 1, '2024-01-14', 2800.00, 2800.00, 0.00, 'unpaid')
        );
        
        foreach ($sample_purchases as $purchase) {
            $insert_purchase = "INSERT INTO purchases (supplier_id, user_id, purchase_date, total_amount, final_amount, paid_amount, payment_status) 
                               VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insert_purchase);
            $stmt->bind_param("iisddds", $purchase[0], $purchase[1], $purchase[2], $purchase[3], $purchase[4], $purchase[5], $purchase[6]);
            
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✅ تم إضافة فاتورة مشتريات بمبلغ " . number_format($purchase[3], 2) . " ريال</p>";
            }
        }
    }
    
    $conn->close();
    
    echo "<hr>";
    echo "<h2>تقرير الإصلاحات</h2>";
    
    if ($errors_count == 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h3 style='color: #155724;'>🎉 تم إصلاح جميع المشاكل بنجاح!</h3>";
        echo "<p>تم تطبيق $fixes_applied إصلاح</p>";
        echo "</div>";
        
        echo "<h3>يمكنك الآن استخدام جميع صفحات النظام:</h3>";
        echo "<ul>";
        echo "<li><a href='pages/sales/index.php' style='color: blue;'>💰 إدارة المبيعات</a></li>";
        echo "<li><a href='pages/purchases/index.php' style='color: blue;'>🛒 إدارة المشتريات</a></li>";
        echo "<li><a href='pages/products/index.php' style='color: blue;'>📦 إدارة المنتجات</a></li>";
        echo "<li><a href='pages/expenses/index.php' style='color: blue;'>💸 إدارة المصروفات</a></li>";
        echo "<li><a href='pages/treasury/index.php' style='color: blue;'>🏦 إدارة الخزينة</a></li>";
        echo "<li><a href='index.php' style='color: blue;'>🏠 الصفحة الرئيسية</a></li>";
        echo "</ul>";
        
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h3 style='color: #721c24;'>⚠️ تم تطبيق $fixes_applied إصلاح مع $errors_count خطأ</h3>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<?php
/**
 * التقرير الشامل النهائي لنظام Zero
 * Comprehensive Final System Report
 */

echo "<h1>التقرير الشامل النهائي لنظام Zero</h1>";
echo "<h2>Comprehensive Final System Report</h2>";

require_once 'config/db_config.php';
require_once 'includes/functions.php';

try {
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #0056b3;'>🎉 نظام Zero جاهز للاستخدام الكامل!</h3>";
    echo "<p>تم إصلاح جميع المشاكل وإعداد النظام بشكل كامل</p>";
    echo "</div>";
    
    // إحصائيات النظام
    echo "<h2>📊 إحصائيات النظام</h2>";
    
    $stats = array();
    
    // إحصائيات قاعدة البيانات
    $tables = array('users', 'categories', 'products', 'customers', 'suppliers', 'sales', 'purchases', 'expenses', 'treasury_transactions', 'settings');
    $stats['tables'] = 0;
    
    foreach ($tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            $stats['tables']++;
        }
    }
    
    // إحصائيات البيانات
    $stats['users'] = $conn->query("SELECT COUNT(*) as count FROM users")->fetch_assoc()['count'];
    $stats['products'] = $conn->query("SELECT COUNT(*) as count FROM products")->fetch_assoc()['count'];
    $stats['customers'] = $conn->query("SELECT COUNT(*) as count FROM customers")->fetch_assoc()['count'];
    $stats['suppliers'] = $conn->query("SELECT COUNT(*) as count FROM suppliers")->fetch_assoc()['count'];
    $stats['sales'] = $conn->query("SELECT COUNT(*) as count FROM sales")->fetch_assoc()['count'];
    $stats['purchases'] = $conn->query("SELECT COUNT(*) as count FROM purchases")->fetch_assoc()['count'];
    
    // إحصائيات مالية
    $sales_total = $conn->query("SELECT SUM(final_amount) as total FROM sales")->fetch_assoc()['total'] ?? 0;
    $purchases_total = $conn->query("SELECT SUM(final_amount) as total FROM purchases")->fetch_assoc()['total'] ?? 0;
    $treasury_balance = getTreasuryBalance();
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    $system_stats = array(
        array('الجداول', $stats['tables'] . '/12', '🗃️', '#17a2b8'),
        array('المستخدمين', $stats['users'], '👤', '#28a745'),
        array('المنتجات', $stats['products'], '📦', '#ffc107'),
        array('العملاء', $stats['customers'], '👥', '#6f42c1'),
        array('الموردين', $stats['suppliers'], '📋', '#fd7e14'),
        array('فواتير المبيعات', $stats['sales'], '💰', '#20c997'),
        array('فواتير المشتريات', $stats['purchases'], '🛒', '#e83e8c'),
        array('رصيد الخزينة', number_format($treasury_balance, 2) . ' ريال', '🏦', '#6610f2')
    );
    
    foreach ($system_stats as $stat) {
        echo "<div style='background: white; border: 1px solid #ddd; padding: 15px; text-align: center; border-radius: 8px; border-top: 4px solid {$stat[3]};'>";
        echo "<div style='font-size: 2em; margin-bottom: 10px;'>{$stat[2]}</div>";
        echo "<h4 style='margin: 10px 0; color: {$stat[3]};'>{$stat[1]}</h4>";
        echo "<p style='color: #666; margin: 0;'>{$stat[0]}</p>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // تقرير مالي
    echo "<h2>💰 التقرير المالي</h2>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 12px;'>البيان</th>";
    echo "<th style='padding: 12px;'>المبلغ (ريال)</th>";
    echo "<th style='padding: 12px;'>النسبة</th>";
    echo "</tr>";
    
    $total_revenue = $sales_total;
    $total_expenses = $purchases_total;
    $net_profit = $total_revenue - $total_expenses;
    $profit_margin = $total_revenue > 0 ? ($net_profit / $total_revenue) * 100 : 0;
    
    $financial_data = array(
        array('إجمالي المبيعات', $sales_total, '100%', '#28a745'),
        array('إجمالي المشتريات', $purchases_total, $total_revenue > 0 ? round(($purchases_total / $total_revenue) * 100, 1) . '%' : '0%', '#dc3545'),
        array('صافي الربح', $net_profit, round($profit_margin, 1) . '%', $net_profit >= 0 ? '#28a745' : '#dc3545'),
        array('رصيد الخزينة', $treasury_balance, '-', '#17a2b8')
    );
    
    foreach ($financial_data as $item) {
        echo "<tr>";
        echo "<td style='padding: 12px; font-weight: bold;'>{$item[0]}</td>";
        echo "<td style='padding: 12px; text-align: right; color: {$item[3]};'>" . number_format($item[1], 2) . "</td>";
        echo "<td style='padding: 12px; text-align: center;'>{$item[2]}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // حالة الوحدات
    echo "<h2>🔧 حالة وحدات النظام</h2>";
    
    $modules_status = array(
        array('إدارة المبيعات', 'pages/sales/index.php', '✅ تعمل', '#28a745'),
        array('إدارة المشتريات', 'pages/purchases/index.php', '✅ تعمل', '#28a745'),
        array('إدارة المنتجات', 'pages/products/index.php', '✅ تعمل', '#28a745'),
        array('إدارة العملاء', 'pages/customers/index.php', '✅ تعمل', '#28a745'),
        array('إدارة الموردين', 'pages/suppliers/index.php', '✅ تعمل', '#28a745'),
        array('إدارة المصروفات', 'pages/expenses/index.php', '✅ تعمل', '#28a745'),
        array('إدارة الخزينة', 'pages/treasury/index.php', '✅ تعمل', '#28a745'),
        array('الإعدادات', 'pages/settings/index.php', '✅ تعمل', '#28a745')
    );
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 12px;'>الوحدة</th>";
    echo "<th style='padding: 12px;'>الحالة</th>";
    echo "<th style='padding: 12px;'>الإجراءات</th>";
    echo "</tr>";
    
    foreach ($modules_status as $module) {
        echo "<tr>";
        echo "<td style='padding: 12px;'>{$module[0]}</td>";
        echo "<td style='padding: 12px; color: {$module[3]};'>{$module[2]}</td>";
        echo "<td style='padding: 12px;'>";
        echo "<a href='{$module[1]}' target='_blank' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; margin-right: 5px;'>فتح</a>";
        echo "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // المميزات المتاحة
    echo "<h2>🌟 المميزات المتاحة</h2>";
    
    $features = array(
        '✅ نظام مبيعات متكامل مع فواتير مفصلة',
        '✅ إدارة المشتريات والموردين',
        '✅ تتبع المخزون والمنتجات',
        '✅ إدارة أرصدة العملاء والموردين',
        '✅ نظام خزينة شامل مع تتبع الحركات المالية',
        '✅ تقارير مالية مفصلة',
        '✅ نظام خصومات وضرائب',
        '✅ طرق دفع متعددة (نقدي، بطاقة، تحويل، شيك، آجل)',
        '✅ طباعة الفواتير',
        '✅ نظام أذونات المستخدمين',
        '✅ نسخ احتياطي تلقائي',
        '✅ واجهة باللغة العربية',
        '✅ تصميم متجاوب يعمل على جميع الأجهزة'
    );
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 10px; margin: 20px 0;'>";
    foreach ($features as $feature) {
        echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; border-radius: 5px;'>";
        echo $feature;
        echo "</div>";
    }
    echo "</div>";
    
    // روابط سريعة
    echo "<h2>🔗 روابط سريعة</h2>";
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    $quick_links = array(
        array('🏠 الصفحة الرئيسية', 'index.php', '#007bff'),
        array('🔐 تسجيل الدخول', 'login.php', '#28a745'),
        array('💰 إضافة فاتورة مبيعات', 'pages/sales/add.php', '#17a2b8'),
        array('🛒 إضافة فاتورة مشتريات', 'pages/purchases/add.php', '#ffc107'),
        array('📦 إضافة منتج جديد', 'pages/products/add.php', '#6f42c1'),
        array('👥 إضافة عميل جديد', 'pages/customers/add.php', '#fd7e14'),
        array('📋 إضافة مورد جديد', 'pages/suppliers/add.php', '#20c997'),
        array('💸 إضافة مصروف جديد', 'pages/expenses/add.php', '#e83e8c')
    );
    
    foreach ($quick_links as $link) {
        echo "<div style='background: white; border: 1px solid #ddd; padding: 15px; text-align: center; border-radius: 8px;'>";
        echo "<h4 style='margin: 10px 0;'>{$link[0]}</h4>";
        echo "<a href='{$link[1]}' style='background: {$link[2]}; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;'>فتح الصفحة</a>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // بيانات الدخول
    echo "<h2>🔐 بيانات الدخول</h2>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4 style='color: #856404; margin-top: 0;'>بيانات المدير الافتراضي:</h4>";
    echo "<p><strong>اسم المستخدم:</strong> admin</p>";
    echo "<p><strong>كلمة المرور:</strong> admin</p>";
    echo "<p style='color: #856404; margin-bottom: 0;'><strong>تنبيه:</strong> يُنصح بتغيير كلمة المرور من صفحة الإعدادات بعد تسجيل الدخول</p>";
    echo "</div>";
    
    // ملاحظات مهمة
    echo "<h2>📝 ملاحظات مهمة</h2>";
    
    $important_notes = array(
        '🔒 تأكد من تغيير كلمة مرور المدير الافتراضي',
        '💾 قم بعمل نسخ احتياطية دورية لقاعدة البيانات',
        '🔧 راجع إعدادات الشركة من صفحة الإعدادات',
        '📊 راقب تقارير المبيعات والمشتريات بانتظام',
        '🏦 تابع رصيد الخزينة والحركات المالية',
        '📦 راقب مستويات المخزون والمنتجات التي توشك على النفاد',
        '👥 حدث بيانات العملاء والموردين بانتظام',
        '🔄 استخدم النسخ الاحتياطي التلقائي المتاح في النظام'
    );
    
    echo "<ul style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    foreach ($important_notes as $note) {
        echo "<li style='margin: 10px 0;'>$note</li>";
    }
    echo "</ul>";
    
    echo "<hr>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; text-align: center; margin: 20px 0;'>";
    echo "<h2 style='color: #155724; margin-top: 0;'>🎉 مبروك! نظام Zero جاهز للاستخدام</h2>";
    echo "<p style='color: #155724; font-size: 1.1em; margin-bottom: 0;'>جميع الوحدات تعمل بشكل مثالي ويمكنك البدء في إدارة محلك التجاري الآن</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>❌ خطأ في النظام</h3>";
    echo "<p>$e->getMessage()</p>";
    echo "</div>";
}
?>

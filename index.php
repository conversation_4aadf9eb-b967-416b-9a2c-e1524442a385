<?php
// استدعاء ملفات الإعدادات والوظائف
require_once "config/config.php";
require_once "config/database.php";
require_once "includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
session_start();
if (!isset($_SESSION["user_id"])) {
    header("Location: login.php");
    exit();
}

$page_title = "لوحة التحكم الرئيسية";
$page_icon = "fas fa-tachometer-alt";
$base_url = "";

// الحصول على إحصائيات للوحة التحكم
$stats = array();

// إجمالي المبيعات اليوم
$today = date('Y-m-d');
$query = "SELECT COUNT(*) as count, SUM(total_amount) as total FROM sales WHERE sale_date = '$today'";
$result = $conn->query($query);
if ($result && $result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $stats['sales_today_count'] = $row['count'] ?? 0;
    $stats['sales_today_amount'] = $row['total'] ?? 0;
} else {
    $stats['sales_today_count'] = 0;
    $stats['sales_today_amount'] = 0;
}

// إجمالي المبيعات الشهر
$first_day_of_month = date('Y-m-01');
$query = "SELECT COUNT(*) as count, SUM(total_amount) as total FROM sales WHERE sale_date >= '$first_day_of_month'";
$result = $conn->query($query);
if ($result && $result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $stats['sales_month_count'] = $row['count'] ?? 0;
    $stats['sales_month_amount'] = $row['total'] ?? 0;
} else {
    $stats['sales_month_count'] = 0;
    $stats['sales_month_amount'] = 0;
}

// عدد العملاء
$query = "SELECT COUNT(*) as count FROM customers";
$result = $conn->query($query);
if ($result && $result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $stats['customers_count'] = $row['count'] ?? 0;
} else {
    $stats['customers_count'] = 0;
}

// عدد الموردين
$query = "SELECT COUNT(*) as count FROM suppliers";
$result = $conn->query($query);
if ($result && $result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $stats['suppliers_count'] = $row['count'] ?? 0;
} else {
    $stats['suppliers_count'] = 0;
}

// عدد المنتجات
$query = "SELECT COUNT(*) as count FROM products";
$result = $conn->query($query);
if ($result && $result->num_rows > 0) {
    $row = $result->fetch_assoc();
    $stats['products_count'] = $row['count'] ?? 0;
} else {
    $stats['products_count'] = 0;
}

// رصيد الخزينة
$stats['treasury_balance'] = getTreasuryBalance();

// المنتجات التي توشك على النفاد
$query = "SELECT p.*, c.name as category_name FROM products p
          LEFT JOIN categories c ON p.category_id = c.id
          WHERE p.stock_quantity <= p.min_stock
          ORDER BY p.stock_quantity ASC LIMIT 5";
$lowStockProducts = $conn->query($query);

// آخر المبيعات
$query = "SELECT s.*, c.name as customer_name, u.name as user_name 
          FROM sales s 
          LEFT JOIN customers c ON s.customer_id = c.id 
          LEFT JOIN users u ON s.user_id = u.id 
          ORDER BY s.id DESC LIMIT 5";
$recentSales = $conn->query($query);

// استدعاء ملف الرأس
require_once "includes/header.php";
?>

<!-- بداية لوحة الإحصائيات -->
<div class="row">
    <div class="col-md-3 col-sm-6">
        <div class="stats-card">
            <div class="stats-icon text-primary">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="stats-value"><?php echo formatMoney($stats['sales_today_amount']); ?></div>
            <div class="stats-label">مبيعات اليوم</div>
            <div class="text-muted small mt-2">عدد الفواتير: <?php echo $stats['sales_today_count']; ?></div>
        </div>
    </div>

    <div class="col-md-3 col-sm-6">
        <div class="stats-card">
            <div class="stats-icon text-success">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stats-value"><?php echo formatMoney($stats['sales_month_amount']); ?></div>
            <div class="stats-label">مبيعات الشهر</div>
            <div class="text-muted small mt-2">عدد الفواتير: <?php echo $stats['sales_month_count']; ?></div>
        </div>
    </div>

    <div class="col-md-3 col-sm-6">
        <div class="stats-card">
            <div class="stats-icon text-info">
                <i class="fas fa-boxes"></i>
            </div>
            <div class="stats-value"><?php echo $stats['products_count']; ?></div>
            <div class="stats-label">المنتجات</div>
            <div class="text-muted small mt-2">عدد العملاء: <?php echo $stats['customers_count']; ?></div>
        </div>
    </div>

    <div class="col-md-3 col-sm-6">
        <div class="stats-card">
            <div class="stats-icon text-warning">
                <i class="fas fa-cash-register"></i>
            </div>
            <div class="stats-value"><?php echo formatMoney($stats['treasury_balance']); ?></div>
            <div class="stats-label">رصيد الخزينة</div>
            <div class="text-muted small mt-2">عدد الموردين: <?php echo $stats['suppliers_count']; ?></div>
        </div>
    </div>
</div>
<!-- نهاية لوحة الإحصائيات -->

<!-- بداية روابط سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="form-card p-0">
            <div class="card-body d-flex flex-wrap gap-2 justify-content-center">
                <a href="pages/sales/add-advanced.php" class="btn btn-primary btn-lg">
                    <i class="fas fa-barcode me-2"></i> فاتورة متطورة (باركود)
                </a>
                <a href="pages/sales/add.php" class="btn btn-outline-primary btn-lg">
                    <i class="fas fa-plus-circle me-2"></i> فاتورة مبيعات عادية
                </a>
                <a href="pages/products/add.php" class="btn btn-success btn-lg">
                    <i class="fas fa-plus-circle me-2"></i> إضافة منتج جديد
                </a>
                <a href="pages/purchases/add.php" class="btn btn-info btn-lg text-white">
                    <i class="fas fa-plus-circle me-2"></i> فاتورة مشتريات جديدة
                </a>
                <a href="pages/expenses/add.php" class="btn btn-warning btn-lg">
                    <i class="fas fa-plus-circle me-2"></i> إضافة مصروف جديد
                </a>
            </div>
        </div>
    </div>
</div>
<!-- نهاية روابط سريعة -->

<div class="row mt-4">
    <!-- بداية المنتجات التي توشك على النفاد -->
    <div class="col-md-6">
        <div class="dashboard-section">
            <h3 class="dashboard-section-title">المنتجات التي توشك على النفاد</h3>

            <div class="data-table">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>اسم المنتج</th>
                            <th>الكود</th>
                            <th>الكمية المتاحة</th>
                            <th>الحد الأدنى</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($lowStockProducts && $lowStockProducts->num_rows > 0): ?>
                            <?php while ($product = $lowStockProducts->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $product['name']; ?></td>
                                    <td><?php echo $product['barcode'] ?? '-'; ?></td>
                                    <td class="<?php echo ($product['stock_quantity'] <= 0) ? 'text-danger' : 'text-warning'; ?> fw-bold">
                                        <?php echo $product['stock_quantity']; ?>
                                    </td>
                                    <td><?php echo $product['min_stock'] ?? '0'; ?></td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="pages/products/view.php?id=<?php echo $product['id']; ?>" class="btn btn-sm btn-info text-white" data-bs-toggle="tooltip" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="pages/purchases/add.php?product_id=<?php echo $product['id']; ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="شراء">
                                                <i class="fas fa-shopping-cart"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="5" class="text-center py-3">لا توجد منتجات توشك على النفاد</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>

                <div class="card-footer text-center">
                    <a href="pages/products/index.php" class="btn btn-outline-primary">
                        <i class="fas fa-boxes me-2"></i> عرض كل المنتجات
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- نهاية المنتجات التي توشك على النفاد -->

    <!-- بداية آخر المبيعات -->
    <div class="col-md-6">
        <div class="dashboard-section">
            <h3 class="dashboard-section-title">آخر المبيعات</h3>

            <div class="data-table">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المبلغ</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($recentSales && $recentSales->num_rows > 0): ?>
                            <?php while ($sale = $recentSales->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo $sale['sale_number'] ?? '-'; ?></td>
                                    <td><?php echo $sale['customer_name'] ?? 'عميل نقدي'; ?></td>
                                    <td><?php echo formatDate($sale['sale_date']); ?></td>
                                    <td><?php echo formatMoney($sale['total_amount']); ?></td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="pages/sales/view.php?id=<?php echo $sale['id']; ?>" class="btn btn-sm btn-info text-white" data-bs-toggle="tooltip" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="pages/sales/print.php?id=<?php echo $sale['id']; ?>" class="btn btn-sm btn-secondary" data-bs-toggle="tooltip" title="طباعة">
                                                <i class="fas fa-print"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="5" class="text-center py-3">لا توجد مبيعات حديثة</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>

                <div class="card-footer text-center">
                    <a href="pages/sales/index.php" class="btn btn-outline-primary">
                        <i class="fas fa-shopping-cart me-2"></i> عرض كل المبيعات
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- نهاية آخر المبيعات -->
</div>

<!-- Modal للحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف <span class="item-name fw-bold">العنصر</span>؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">تأكيد الحذف</a>
            </div>
        </div>
    </div>
</div>

<?php
// استدعاء ملف التذييل
require_once "includes/footer.php";
?>
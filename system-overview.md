# نظام Zero - نظام إدارة المحلات التجارية
## Zero System - Store Management System

### نظرة عامة على النظام / System Overview

هذا نظام شامل لإدارة المحلات التجارية مطور بـ PHP و MySQL، يتضمن جميع الوظائف الأساسية لإدارة المبيعات والمشتريات والمخزون.

This is a comprehensive store management system developed with PHP & MySQL, including all essential functions for managing sales, purchases, and inventory.

### الملفات الرئيسية / Main Files

#### 1. ملفات الإعدادات / Configuration Files
- `config/config.php` - الإعدادات العامة للتطبيق
- `config/db_config.php` - إعدادات قاعدة البيانات
- `config/database.php` - ملف الاتصال بقاعدة البيانات

#### 2. الملفات الأساسية / Core Files
- `index.php` - الصفحة الرئيسية (لوحة التحكم)
- `login.php` - صفحة تسجيل الدخول
- `logout.php` - تسجيل الخروج
- `profile.php` - الملف الشخصي

#### 3. ملفات المساعدة / Helper Files
- `includes/functions.php` - الوظائف المساعدة
- `includes/header.php` - رأس الصفحة
- `includes/footer.php` - تذييل الصفحة
- `includes/session-helper.php` - مساعد الجلسات

#### 4. قاعدة البيانات / Database
- `database/zero.sql` - ملف قاعدة البيانات الكاملة

### الوحدات الرئيسية / Main Modules

#### 1. إدارة المبيعات / Sales Management
- `pages/sales/` - جميع ملفات المبيعات
- إضافة فواتير مبيعات عادية ومتطورة
- طباعة الفواتير
- تتبع المبيعات اليومية والشهرية

#### 2. إدارة المشتريات / Purchases Management
- `pages/purchases/` - جميع ملفات المشتريات
- إضافة فواتير المشتريات
- تتبع المشتريات من الموردين

#### 3. إدارة المنتجات / Products Management
- `pages/products/` - إدارة المنتجات
- إضافة وتعديل المنتجات
- تتبع المخزون
- تنبيهات نفاد المخزون

#### 4. إدارة العملاء / Customers Management
- `pages/customers/` - إدارة العملاء
- إضافة وتعديل بيانات العملاء
- تتبع مبيعات العملاء

#### 5. إدارة الموردين / Suppliers Management
- `pages/suppliers/` - إدارة الموردين
- إضافة وتعديل بيانات الموردين
- تتبع المشتريات من الموردين

#### 6. إدارة المصروفات / Expenses Management
- `pages/expenses/` - إدارة المصروفات
- تسجيل المصروفات اليومية
- تصنيف المصروفات

#### 7. إدارة الخزينة / Treasury Management
- `pages/treasury/` - إدارة الخزينة
- تتبع الإيرادات والمصروفات
- رصيد الخزينة

#### 8. الإعدادات / Settings
- `pages/settings/` - إعدادات النظام
- إعدادات الشركة
- إعدادات المستخدمين
- النسخ الاحتياطي

### المميزات الخاصة / Special Features

#### 1. نظام الباركود / Barcode System
- قراءة الباركود في فواتير المبيعات المتطورة
- إضافة منتجات سريعة عبر الباركود

#### 2. النسخ الاحتياطي التلقائي / Automatic Backup
- `backups/` - مجلد النسخ الاحتياطية
- نسخ احتياطية تلقائية لقاعدة البيانات

#### 3. رفع الملفات / File Uploads
- `uploads/logo/` - شعارات الشركة
- `uploads/products/` - صور المنتجات

#### 4. الأصول / Assets
- `assets/css/` - ملفات التنسيق
- `assets/js/` - ملفات JavaScript
- `assets/images/` - الصور
- `assets/fonts/` - الخطوط

### بيانات الدخول الافتراضية / Default Login Credentials
- **اسم المستخدم / Username:** admin
- **كلمة المرور / Password:** admin

### متطلبات التشغيل / System Requirements
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx Web Server
- WAMP/XAMPP للتطوير المحلي

### كيفية التشغيل / How to Run

1. تأكد من تشغيل خدمات WAMP
2. افتح المتصفح واذهب إلى: `http://localhost/zero4`
3. أو استخدم ملف الاختبار: `http://localhost/zero4/test-connection.php`
4. سجل الدخول باستخدام البيانات الافتراضية

### الحالة الحالية / Current Status
✅ النظام جاهز للاستخدام
✅ قاعدة البيانات مُعدة
✅ جميع الوحدات متاحة
✅ واجهة المستخدم باللغة العربية
✅ نظام الأمان مُفعل

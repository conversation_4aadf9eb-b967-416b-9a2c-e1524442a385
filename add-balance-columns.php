<?php
/**
 * ملف إضافة أعمدة الرصيد للموردين والعملاء
 * Add Balance Columns Script
 */

echo "<h1>إضافة أعمدة الرصيد - Add Balance Columns</h1>";

$db_host = "localhost";
$db_user = "root";
$db_pass = "";
$db_name = "zero";

try {
    // الاتصال بقاعدة البيانات
    $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
    $conn->set_charset("utf8mb4");
    
    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }
    
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // إضافة عمود balance لجدول suppliers
    echo "<h2>إضافة عمود الرصيد لجدول الموردين</h2>";
    
    // التحقق من وجود العمود أولاً
    $check_suppliers = $conn->query("SHOW COLUMNS FROM suppliers LIKE 'balance'");
    
    if ($check_suppliers && $check_suppliers->num_rows > 0) {
        echo "<p style='color: blue;'>ℹ️ عمود 'balance' موجود بالفعل في جدول suppliers</p>";
    } else {
        $add_suppliers_balance = "ALTER TABLE suppliers ADD COLUMN balance DECIMAL(10,2) DEFAULT 0.00 AFTER email";
        
        if ($conn->query($add_suppliers_balance) === TRUE) {
            echo "<p style='color: green;'>✅ تم إضافة عمود 'balance' لجدول suppliers بنجاح</p>";
        } else {
            echo "<p style='color: red;'>❌ خطأ في إضافة عمود 'balance' لجدول suppliers: " . $conn->error . "</p>";
        }
    }
    
    // إضافة عمود balance لجدول customers
    echo "<h2>إضافة عمود الرصيد لجدول العملاء</h2>";
    
    $check_customers = $conn->query("SHOW COLUMNS FROM customers LIKE 'balance'");
    
    if ($check_customers && $check_customers->num_rows > 0) {
        echo "<p style='color: blue;'>ℹ️ عمود 'balance' موجود بالفعل في جدول customers</p>";
    } else {
        $add_customers_balance = "ALTER TABLE customers ADD COLUMN balance DECIMAL(10,2) DEFAULT 0.00 AFTER email";
        
        if ($conn->query($add_customers_balance) === TRUE) {
            echo "<p style='color: green;'>✅ تم إضافة عمود 'balance' لجدول customers بنجاح</p>";
        } else {
            echo "<p style='color: red;'>❌ خطأ في إضافة عمود 'balance' لجدول customers: " . $conn->error . "</p>";
        }
    }
    
    // إضافة بعض البيانات التجريبية للموردين
    echo "<h2>إضافة بيانات تجريبية للموردين</h2>";
    
    $suppliers_count = $conn->query("SELECT COUNT(*) as count FROM suppliers")->fetch_assoc()['count'];
    
    if ($suppliers_count == 0) {
        $sample_suppliers = array(
            array('شركة الأجهزة المتقدمة', 'Advanced Tech Co.', '0501234567', '<EMAIL>', 'الرياض، المملكة العربية السعودية', 0),
            array('مؤسسة الإلكترونيات الحديثة', 'Modern Electronics Est.', '0509876543', '<EMAIL>', 'جدة، المملكة العربية السعودية', -1500.00),
            array('شركة المعدات الصناعية', 'Industrial Equipment Co.', '0551122334', '<EMAIL>', 'الدمام، المملكة العربية السعودية', 2500.00),
            array('مورد الأجهزة المنزلية', 'Home Appliances Supplier', '0556677889', '<EMAIL>', 'مكة المكرمة، المملكة العربية السعودية', 0),
            array('شركة التقنيات المتطورة', 'Advanced Technologies Inc.', '0544455667', '<EMAIL>', 'المدينة المنورة، المملكة العربية السعودية', -800.00)
        );
        
        foreach ($sample_suppliers as $supplier) {
            $insert_supplier = "INSERT INTO suppliers (name, company, phone, email, address, balance) VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insert_supplier);
            $stmt->bind_param("sssssd", $supplier[0], $supplier[1], $supplier[2], $supplier[3], $supplier[4], $supplier[5]);
            
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✅ تم إضافة المورد: " . $supplier[0] . " - الرصيد: " . number_format($supplier[5], 2) . " ريال</p>";
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة المورد: " . $supplier[0] . "</p>";
            }
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ يوجد $suppliers_count مورد في النظام</p>";
        
        // تحديث أرصدة الموردين الموجودين بقيم عشوائية للاختبار
        $update_balances = "UPDATE suppliers SET balance = CASE 
                           WHEN id % 3 = 0 THEN ROUND((RAND() * 5000) - 2500, 2)
                           WHEN id % 3 = 1 THEN ROUND(RAND() * 3000, 2)
                           ELSE ROUND((RAND() * 2000) - 1000, 2)
                           END
                           WHERE balance = 0";
        
        if ($conn->query($update_balances) === TRUE) {
            echo "<p style='color: green;'>✅ تم تحديث أرصدة الموردين الموجودين</p>";
        }
    }
    
    // إضافة بعض البيانات التجريبية للعملاء
    echo "<h2>إضافة بيانات تجريبية للعملاء</h2>";
    
    $customers_count = $conn->query("SELECT COUNT(*) as count FROM customers")->fetch_assoc()['count'];
    
    if ($customers_count == 0) {
        $sample_customers = array(
            array('أحمد محمد العلي', '0501111111', '<EMAIL>', 'الرياض، حي النخيل', 500.00),
            array('فاطمة سعد الأحمد', '0502222222', '<EMAIL>', 'جدة، حي الصفا', -200.00),
            array('محمد عبدالله السعد', '0503333333', '<EMAIL>', 'الدمام، حي الفيصلية', 0),
            array('نورا خالد المحمد', '0504444444', '<EMAIL>', 'مكة، حي العزيزية', 1200.00),
            array('عبدالرحمن أحمد الخالد', '0505555555', '<EMAIL>', 'المدينة، حي قباء', -150.00)
        );
        
        foreach ($sample_customers as $customer) {
            $insert_customer = "INSERT INTO customers (name, phone, email, address, balance) VALUES (?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($insert_customer);
            $stmt->bind_param("ssssd", $customer[0], $customer[1], $customer[2], $customer[3], $customer[4]);
            
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✅ تم إضافة العميل: " . $customer[0] . " - الرصيد: " . number_format($customer[4], 2) . " ريال</p>";
            } else {
                echo "<p style='color: red;'>❌ خطأ في إضافة العميل: " . $customer[0] . "</p>";
            }
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ يوجد $customers_count عميل في النظام</p>";
        
        // تحديث أرصدة العملاء الموجودين
        $update_customer_balances = "UPDATE customers SET balance = CASE 
                                    WHEN id % 3 = 0 THEN ROUND((RAND() * 2000) - 500, 2)
                                    WHEN id % 3 = 1 THEN ROUND(RAND() * 1500, 2)
                                    ELSE ROUND((RAND() * 1000) - 300, 2)
                                    END
                                    WHERE balance = 0";
        
        if ($conn->query($update_customer_balances) === TRUE) {
            echo "<p style='color: green;'>✅ تم تحديث أرصدة العملاء الموجودين</p>";
        }
    }
    
    // عرض إحصائيات الأرصدة
    echo "<h2>إحصائيات الأرصدة</h2>";
    
    // إحصائيات الموردين
    $suppliers_stats = $conn->query("SELECT 
                                    COUNT(*) as total,
                                    COUNT(CASE WHEN balance > 0 THEN 1 END) as positive,
                                    COUNT(CASE WHEN balance < 0 THEN 1 END) as negative,
                                    COUNT(CASE WHEN balance = 0 THEN 1 END) as zero,
                                    SUM(CASE WHEN balance > 0 THEN balance ELSE 0 END) as total_positive,
                                    SUM(CASE WHEN balance < 0 THEN ABS(balance) ELSE 0 END) as total_negative
                                    FROM suppliers")->fetch_assoc();
    
    echo "<h3>الموردين:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>إجمالي الموردين</th>";
    echo "<th>مستحقات لنا</th>";
    echo "<th>مستحقات علينا</th>";
    echo "<th>رصيد صفر</th>";
    echo "<th>إجمالي المستحقات لنا</th>";
    echo "<th>إجمالي المستحقات علينا</th>";
    echo "</tr>";
    echo "<tr>";
    echo "<td style='text-align: center;'>" . $suppliers_stats['total'] . "</td>";
    echo "<td style='text-align: center; color: green;'>" . $suppliers_stats['positive'] . "</td>";
    echo "<td style='text-align: center; color: red;'>" . $suppliers_stats['negative'] . "</td>";
    echo "<td style='text-align: center;'>" . $suppliers_stats['zero'] . "</td>";
    echo "<td style='text-align: center; color: green;'>" . number_format($suppliers_stats['total_positive'], 2) . " ريال</td>";
    echo "<td style='text-align: center; color: red;'>" . number_format($suppliers_stats['total_negative'], 2) . " ريال</td>";
    echo "</tr>";
    echo "</table>";
    
    // إحصائيات العملاء
    $customers_stats = $conn->query("SELECT 
                                    COUNT(*) as total,
                                    COUNT(CASE WHEN balance > 0 THEN 1 END) as positive,
                                    COUNT(CASE WHEN balance < 0 THEN 1 END) as negative,
                                    COUNT(CASE WHEN balance = 0 THEN 1 END) as zero,
                                    SUM(CASE WHEN balance > 0 THEN balance ELSE 0 END) as total_positive,
                                    SUM(CASE WHEN balance < 0 THEN ABS(balance) ELSE 0 END) as total_negative
                                    FROM customers")->fetch_assoc();
    
    echo "<h3>العملاء:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>إجمالي العملاء</th>";
    echo "<th>مستحقات لنا</th>";
    echo "<th>مستحقات علينا</th>";
    echo "<th>رصيد صفر</th>";
    echo "<th>إجمالي المستحقات لنا</th>";
    echo "<th>إجمالي المستحقات علينا</th>";
    echo "</tr>";
    echo "<tr>";
    echo "<td style='text-align: center;'>" . $customers_stats['total'] . "</td>";
    echo "<td style='text-align: center; color: green;'>" . $customers_stats['positive'] . "</td>";
    echo "<td style='text-align: center; color: red;'>" . $customers_stats['negative'] . "</td>";
    echo "<td style='text-align: center;'>" . $customers_stats['zero'] . "</td>";
    echo "<td style='text-align: center; color: green;'>" . number_format($customers_stats['total_positive'], 2) . " ريال</td>";
    echo "<td style='text-align: center; color: red;'>" . number_format($customers_stats['total_negative'], 2) . " ريال</td>";
    echo "</tr>";
    echo "</table>";
    
    $conn->close();
    
    echo "<hr>";
    echo "<h3 style='color: green;'>🎉 تم إضافة أعمدة الرصيد بنجاح!</h3>";
    echo "<p>يمكنك الآن استخدام صفحات الموردين والعملاء:</p>";
    echo "<ul>";
    echo "<li><a href='pages/suppliers/index.php' style='color: blue;'>📋 إدارة الموردين</a></li>";
    echo "<li><a href='pages/customers/index.php' style='color: blue;'>👥 إدارة العملاء</a></li>";
    echo "<li><a href='index.php' style='color: blue;'>🏠 الصفحة الرئيسية</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

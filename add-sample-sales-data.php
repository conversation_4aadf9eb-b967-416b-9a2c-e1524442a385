<?php
/**
 * ملف إضافة بيانات تجريبية للمبيعات والمشتريات
 * Add Sample Sales and Purchases Data
 */

echo "<h1>إضافة بيانات تجريبية للمبيعات والمشتريات</h1>";

require_once 'config/db_config.php';
require_once 'includes/functions.php';

try {
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // حذف البيانات التجريبية القديمة
    echo "<h2>تنظيف البيانات القديمة</h2>";
    
    $conn->query("DELETE FROM sale_items");
    $conn->query("DELETE FROM sales");
    $conn->query("DELETE FROM purchase_items");
    $conn->query("DELETE FROM purchases");
    
    echo "<p style='color: blue;'>ℹ️ تم حذف البيانات التجريبية القديمة</p>";
    
    // إضافة بعض المنتجات التجريبية إذا لم تكن موجودة
    echo "<h2>إضافة منتجات تجريبية</h2>";
    
    $products_count = $conn->query("SELECT COUNT(*) as count FROM products")->fetch_assoc()['count'];
    
    if ($products_count == 0) {
        $sample_products = array(
            array('لابتوب ديل', 'LAP001', 1, 15000.00, 18000.00, 10, 2),
            array('ماوس لاسلكي', 'MOU001', 1, 150.00, 200.00, 50, 5),
            array('كيبورد ميكانيكي', 'KEY001', 1, 800.00, 1200.00, 25, 3),
            array('شاشة 24 بوصة', 'MON001', 1, 2500.00, 3200.00, 15, 2),
            array('سماعات بلوتوث', 'HEA001', 1, 300.00, 450.00, 30, 5),
            array('كاميرا ويب', 'CAM001', 1, 400.00, 600.00, 20, 3),
            array('هارد خارجي 1TB', 'HDD001', 1, 600.00, 850.00, 12, 2),
            array('فلاشة 64GB', 'USB001', 1, 80.00, 120.00, 100, 10)
        );
        
        foreach ($sample_products as $product) {
            $insert_product = "INSERT INTO products (name, barcode, category_id, purchase_price, selling_price, stock_quantity, min_stock, cost_price, is_active) 
                              VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)";
            $stmt = $conn->prepare($insert_product);
            $stmt->bind_param("ssiddiii", $product[0], $product[1], $product[2], $product[3], $product[4], $product[5], $product[6], $product[3]);
            
            if ($stmt->execute()) {
                echo "<p style='color: green;'>✅ تم إضافة المنتج: " . $product[0] . "</p>";
            }
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ يوجد $products_count منتج في النظام</p>";
    }
    
    // إضافة مبيعات تجريبية مفصلة
    echo "<h2>إضافة مبيعات تجريبية</h2>";
    
    $sample_sales = array(
        array(
            'customer_id' => 1,
            'sale_date' => '2024-01-15',
            'total_amount' => 18650.00,
            'discount' => 5.00,
            'discount_amount' => 932.50,
            'tax_amount' => 0.00,
            'final_amount' => 17717.50,
            'paid_amount' => 17717.50,
            'payment_status' => 'paid',
            'payment_method' => 'cash',
            'notes' => 'عميل مميز - خصم خاص',
            'items' => array(
                array('product_id' => 1, 'quantity' => 1, 'unit_price' => 18000.00),
                array('product_id' => 2, 'quantity' => 2, 'unit_price' => 200.00),
                array('product_id' => 5, 'quantity' => 1, 'unit_price' => 450.00)
            )
        ),
        array(
            'customer_id' => 2,
            'sale_date' => '2024-01-16',
            'total_amount' => 4000.00,
            'discount' => 0.00,
            'discount_amount' => 0.00,
            'tax_amount' => 600.00,
            'final_amount' => 4600.00,
            'paid_amount' => 3000.00,
            'payment_status' => 'partial',
            'payment_method' => 'card',
            'notes' => 'دفع جزئي - المتبقي على الحساب',
            'items' => array(
                array('product_id' => 4, 'quantity' => 1, 'unit_price' => 3200.00),
                array('product_id' => 3, 'quantity' => 1, 'unit_price' => 1200.00),
                array('product_id' => 8, 'quantity' => 5, 'unit_price' => 120.00)
            )
        ),
        array(
            'customer_id' => null,
            'sale_date' => '2024-01-17',
            'total_amount' => 1550.00,
            'discount' => 0.00,
            'discount_amount' => 0.00,
            'tax_amount' => 0.00,
            'final_amount' => 1550.00,
            'paid_amount' => 1550.00,
            'payment_status' => 'paid',
            'payment_method' => 'cash',
            'notes' => 'عميل نقدي',
            'items' => array(
                array('product_id' => 6, 'quantity' => 2, 'unit_price' => 600.00),
                array('product_id' => 2, 'quantity' => 1, 'unit_price' => 200.00),
                array('product_id' => 8, 'quantity' => 3, 'unit_price' => 120.00)
            )
        ),
        array(
            'customer_id' => 3,
            'sale_date' => '2024-01-18',
            'total_amount' => 2550.00,
            'discount' => 10.00,
            'discount_amount' => 255.00,
            'tax_amount' => 0.00,
            'final_amount' => 2295.00,
            'paid_amount' => 0.00,
            'payment_status' => 'unpaid',
            'payment_method' => 'credit',
            'notes' => 'بيع آجل - سداد خلال 30 يوم',
            'items' => array(
                array('product_id' => 4, 'quantity' => 1, 'unit_price' => 3200.00),
                array('product_id' => 7, 'quantity' => 1, 'unit_price' => 850.00)
            )
        ),
        array(
            'customer_id' => 4,
            'sale_date' => '2024-01-19',
            'total_amount' => 3750.00,
            'discount' => 0.00,
            'discount_amount' => 0.00,
            'tax_amount' => 562.50,
            'final_amount' => 4312.50,
            'paid_amount' => 4312.50,
            'payment_status' => 'paid',
            'payment_method' => 'bank_transfer',
            'notes' => 'تحويل بنكي',
            'items' => array(
                array('product_id' => 3, 'quantity' => 2, 'unit_price' => 1200.00),
                array('product_id' => 5, 'quantity' => 3, 'unit_price' => 450.00)
            )
        )
    );
    
    foreach ($sample_sales as $index => $sale) {
        // إدراج فاتورة المبيعات
        $sale_number = 'S' . str_pad($index + 1, 6, '0', STR_PAD_LEFT);
        
        $insert_sale = "INSERT INTO sales (sale_number, customer_id, user_id, sale_date, total_amount, discount, discount_amount, tax_amount, final_amount, paid_amount, payment_status, payment_method, notes) 
                       VALUES (?, ?, 1, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($insert_sale);
        $stmt->bind_param("sisddddddss", 
            $sale_number,
            $sale['customer_id'],
            $sale['sale_date'],
            $sale['total_amount'],
            $sale['discount'],
            $sale['discount_amount'],
            $sale['tax_amount'],
            $sale['final_amount'],
            $sale['paid_amount'],
            $sale['payment_status'],
            $sale['payment_method'],
            $sale['notes']
        );
        
        if ($stmt->execute()) {
            $sale_id = $conn->insert_id;
            echo "<p style='color: green;'>✅ تم إضافة فاتورة مبيعات رقم $sale_number بمبلغ " . number_format($sale['final_amount'], 2) . " ريال</p>";
            
            // إدراج تفاصيل الفاتورة
            foreach ($sale['items'] as $item) {
                $total_price = $item['quantity'] * $item['unit_price'];
                
                $insert_item = "INSERT INTO sale_items (sale_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)";
                $item_stmt = $conn->prepare($insert_item);
                $item_stmt->bind_param("iiidd", $sale_id, $item['product_id'], $item['quantity'], $item['unit_price'], $total_price);
                $item_stmt->execute();
                
                // تحديث المخزون
                $update_stock = "UPDATE products SET stock_quantity = stock_quantity - ? WHERE id = ?";
                $stock_stmt = $conn->prepare($update_stock);
                $stock_stmt->bind_param("ii", $item['quantity'], $item['product_id']);
                $stock_stmt->execute();
            }
            
            // إضافة حركة مالية للخزينة
            if ($sale['paid_amount'] > 0) {
                addTreasuryTransaction('sales', $sale_id, $sale['paid_amount'], "مبيعات فاتورة رقم $sale_number", 1, $sale['sale_date']);
            }
            
            // تحديث رصيد العميل إذا كان هناك مبلغ متبقي
            if ($sale['customer_id'] && ($sale['final_amount'] - $sale['paid_amount']) > 0) {
                $remaining = $sale['final_amount'] - $sale['paid_amount'];
                updateCustomerBalance($sale['customer_id'], $remaining, "فاتورة مبيعات رقم $sale_number");
            }
        }
    }
    
    // إضافة مشتريات تجريبية
    echo "<h2>إضافة مشتريات تجريبية</h2>";
    
    $sample_purchases = array(
        array(
            'supplier_id' => 1,
            'purchase_date' => '2024-01-10',
            'total_amount' => 45000.00,
            'discount' => 2.00,
            'discount_amount' => 900.00,
            'tax_amount' => 0.00,
            'final_amount' => 44100.00,
            'paid_amount' => 44100.00,
            'payment_status' => 'paid',
            'payment_method' => 'bank_transfer',
            'notes' => 'شحنة أجهزة كمبيوتر',
            'items' => array(
                array('product_id' => 1, 'quantity' => 3, 'unit_price' => 15000.00)
            )
        ),
        array(
            'supplier_id' => 2,
            'purchase_date' => '2024-01-12',
            'total_amount' => 8500.00,
            'discount' => 0.00,
            'discount_amount' => 0.00,
            'tax_amount' => 1275.00,
            'final_amount' => 9775.00,
            'paid_amount' => 5000.00,
            'payment_status' => 'partial',
            'payment_method' => 'cash',
            'notes' => 'دفع جزئي - المتبقي على الحساب',
            'items' => array(
                array('product_id' => 2, 'quantity' => 20, 'unit_price' => 150.00),
                array('product_id' => 3, 'quantity' => 10, 'unit_price' => 800.00)
            )
        )
    );
    
    foreach ($sample_purchases as $index => $purchase) {
        $purchase_number = 'P' . str_pad($index + 1, 6, '0', STR_PAD_LEFT);
        
        $insert_purchase = "INSERT INTO purchases (purchase_number, supplier_id, user_id, purchase_date, total_amount, discount, discount_amount, tax_amount, final_amount, paid_amount, payment_status, payment_method, notes) 
                           VALUES (?, ?, 1, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($insert_purchase);
        $stmt->bind_param("sisddddddss", 
            $purchase_number,
            $purchase['supplier_id'],
            $purchase['purchase_date'],
            $purchase['total_amount'],
            $purchase['discount'],
            $purchase['discount_amount'],
            $purchase['tax_amount'],
            $purchase['final_amount'],
            $purchase['paid_amount'],
            $purchase['payment_status'],
            $purchase['payment_method'],
            $purchase['notes']
        );
        
        if ($stmt->execute()) {
            $purchase_id = $conn->insert_id;
            echo "<p style='color: green;'>✅ تم إضافة فاتورة مشتريات رقم $purchase_number بمبلغ " . number_format($purchase['final_amount'], 2) . " ريال</p>";
            
            // إدراج تفاصيل الفاتورة
            foreach ($purchase['items'] as $item) {
                $total_price = $item['quantity'] * $item['unit_price'];
                
                $insert_item = "INSERT INTO purchase_items (purchase_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)";
                $item_stmt = $conn->prepare($insert_item);
                $item_stmt->bind_param("iiidd", $purchase_id, $item['product_id'], $item['quantity'], $item['unit_price'], $total_price);
                $item_stmt->execute();
                
                // تحديث المخزون
                $update_stock = "UPDATE products SET stock_quantity = stock_quantity + ? WHERE id = ?";
                $stock_stmt = $conn->prepare($update_stock);
                $stock_stmt->bind_param("ii", $item['quantity'], $item['product_id']);
                $stock_stmt->execute();
            }
            
            // إضافة حركة مالية للخزينة
            if ($purchase['paid_amount'] > 0) {
                addTreasuryTransaction('purchases', $purchase_id, -$purchase['paid_amount'], "مشتريات فاتورة رقم $purchase_number", 1, $purchase['purchase_date']);
            }
            
            // تحديث رصيد المورد إذا كان هناك مبلغ متبقي
            if ($purchase['supplier_id'] && ($purchase['final_amount'] - $purchase['paid_amount']) > 0) {
                $remaining = $purchase['final_amount'] - $purchase['paid_amount'];
                updateSupplierBalance($purchase['supplier_id'], -$remaining, "فاتورة مشتريات رقم $purchase_number");
            }
        }
    }
    
    echo "<hr>";
    echo "<h2>تم إضافة البيانات التجريبية بنجاح! 🎉</h2>";
    
    // عرض ملخص البيانات
    $sales_summary = $conn->query("SELECT COUNT(*) as count, SUM(final_amount) as total FROM sales")->fetch_assoc();
    $purchases_summary = $conn->query("SELECT COUNT(*) as count, SUM(final_amount) as total FROM purchases")->fetch_assoc();
    $treasury_balance = getTreasuryBalance();
    
    echo "<h3>ملخص البيانات:</h3>";
    echo "<ul>";
    echo "<li><strong>عدد فواتير المبيعات:</strong> " . $sales_summary['count'] . " فاتورة</li>";
    echo "<li><strong>إجمالي المبيعات:</strong> " . number_format($sales_summary['total'], 2) . " ريال</li>";
    echo "<li><strong>عدد فواتير المشتريات:</strong> " . $purchases_summary['count'] . " فاتورة</li>";
    echo "<li><strong>إجمالي المشتريات:</strong> " . number_format($purchases_summary['total'], 2) . " ريال</li>";
    echo "<li><strong>رصيد الخزينة:</strong> " . number_format($treasury_balance, 2) . " ريال</li>";
    echo "</ul>";
    
    echo "<h3>يمكنك الآن استخدام النظام:</h3>";
    echo "<ul>";
    echo "<li><a href='pages/sales/index.php' style='color: blue;'>💰 إدارة المبيعات</a></li>";
    echo "<li><a href='pages/purchases/index.php' style='color: blue;'>🛒 إدارة المشتريات</a></li>";
    echo "<li><a href='pages/products/index.php' style='color: blue;'>📦 إدارة المنتجات</a></li>";
    echo "<li><a href='pages/treasury/index.php' style='color: blue;'>🏦 إدارة الخزينة</a></li>";
    echo "<li><a href='index.php' style='color: blue;'>🏠 الصفحة الرئيسية</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

<?php
/**
 * فحص نهائي للأخطاء في النظام
 * Final Error Check for System
 */

echo "<h1>الفحص النهائي للأخطاء في نظام Zero</h1>";
echo "<h2>Final Error Check for Zero System</h2>";

require_once 'config/db_config.php';
require_once 'includes/functions.php';

session_start();
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['user_name'] = 'مدير النظام';
$_SESSION['user_role'] = 'admin';
$_SESSION['role'] = 'admin';

try {
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: #0056b3;'>🔍 بدء الفحص النهائي للأخطاء</h3>";
    echo "<p>سيتم فحص جميع الصفحات والوظائف للتأكد من عدم وجود أخطاء</p>";
    echo "</div>";
    
    $total_tests = 0;
    $passed_tests = 0;
    $failed_tests = 0;
    
    // اختبار الصفحات الرئيسية
    echo "<h2>1. اختبار الصفحات الرئيسية</h2>";
    
    $main_pages = array(
        array('الصفحة الرئيسية', 'index.php'),
        array('تسجيل الدخول', 'login.php')
    );
    
    foreach ($main_pages as $page) {
        $total_tests++;
        echo "<h4>اختبار: {$page[0]}</h4>";
        
        ob_start();
        $error_occurred = false;
        
        try {
            if (file_exists($page[1])) {
                include $page[1];
                $content = ob_get_contents();
                
                if (strpos($content, 'Fatal error') !== false || 
                    strpos($content, 'Parse error') !== false ||
                    strpos($content, 'mysqli_sql_exception') !== false ||
                    strpos($content, 'Deprecated:') !== false ||
                    strpos($content, 'Warning:') !== false) {
                    $error_occurred = true;
                }
            } else {
                $error_occurred = true;
            }
        } catch (Exception $e) {
            $error_occurred = true;
        }
        
        ob_end_clean();
        
        if (!$error_occurred) {
            echo "<p style='color: green;'>✅ نجح الاختبار</p>";
            $passed_tests++;
        } else {
            echo "<p style='color: red;'>❌ فشل الاختبار</p>";
            $failed_tests++;
        }
    }
    
    // اختبار صفحات المبيعات
    echo "<h2>2. اختبار صفحات المبيعات</h2>";
    
    $sales_pages = array(
        array('قائمة المبيعات', 'pages/sales/index.php'),
        array('عرض فاتورة مبيعات', 'pages/sales/view.php?id=1'),
        array('طباعة فاتورة مبيعات', 'pages/sales/print.php?id=1'),
        array('إضافة فاتورة مبيعات', 'pages/sales/add.php')
    );
    
    foreach ($sales_pages as $page) {
        $total_tests++;
        echo "<h4>اختبار: {$page[0]}</h4>";
        
        // محاكاة طلب GET
        if (strpos($page[1], '?') !== false) {
            $parts = explode('?', $page[1]);
            $file = $parts[0];
            parse_str($parts[1], $_GET);
        } else {
            $file = $page[1];
        }
        
        ob_start();
        $error_occurred = false;
        
        try {
            if (file_exists($file)) {
                include $file;
                $content = ob_get_contents();
                
                if (strpos($content, 'Fatal error') !== false || 
                    strpos($content, 'Parse error') !== false ||
                    strpos($content, 'mysqli_sql_exception') !== false ||
                    strpos($content, 'Deprecated:') !== false ||
                    strpos($content, 'Warning:') !== false) {
                    $error_occurred = true;
                }
            } else {
                $error_occurred = true;
            }
        } catch (Exception $e) {
            $error_occurred = true;
        }
        
        ob_end_clean();
        
        if (!$error_occurred) {
            echo "<p style='color: green;'>✅ نجح الاختبار - <a href='{$page[1]}' target='_blank'>فتح الصفحة</a></p>";
            $passed_tests++;
        } else {
            echo "<p style='color: red;'>❌ فشل الاختبار</p>";
            $failed_tests++;
        }
    }
    
    // اختبار صفحات المشتريات
    echo "<h2>3. اختبار صفحات المشتريات</h2>";
    
    $purchases_pages = array(
        array('قائمة المشتريات', 'pages/purchases/index.php'),
        array('عرض فاتورة مشتريات', 'pages/purchases/view.php?id=1'),
        array('طباعة فاتورة مشتريات', 'pages/purchases/print.php?id=1'),
        array('إضافة فاتورة مشتريات', 'pages/purchases/add.php')
    );
    
    foreach ($purchases_pages as $page) {
        $total_tests++;
        echo "<h4>اختبار: {$page[0]}</h4>";
        
        if (strpos($page[1], '?') !== false) {
            $parts = explode('?', $page[1]);
            $file = $parts[0];
            parse_str($parts[1], $_GET);
        } else {
            $file = $page[1];
        }
        
        ob_start();
        $error_occurred = false;
        
        try {
            if (file_exists($file)) {
                include $file;
                $content = ob_get_contents();
                
                if (strpos($content, 'Fatal error') !== false || 
                    strpos($content, 'Parse error') !== false ||
                    strpos($content, 'mysqli_sql_exception') !== false ||
                    strpos($content, 'Deprecated:') !== false ||
                    strpos($content, 'Warning:') !== false) {
                    $error_occurred = true;
                }
            } else {
                $error_occurred = true;
            }
        } catch (Exception $e) {
            $error_occurred = true;
        }
        
        ob_end_clean();
        
        if (!$error_occurred) {
            echo "<p style='color: green;'>✅ نجح الاختبار - <a href='{$page[1]}' target='_blank'>فتح الصفحة</a></p>";
            $passed_tests++;
        } else {
            echo "<p style='color: red;'>❌ فشل الاختبار</p>";
            $failed_tests++;
        }
    }
    
    // اختبار الوظائف المساعدة الجديدة
    echo "<h2>4. اختبار الوظائف المساعدة الجديدة</h2>";
    
    $function_tests = array(
        array('safe_output', function() { return safe_output(null, 'افتراضي'); }),
        array('safe_customer_name', function() { return safe_customer_name(null); }),
        array('safe_supplier_name', function() { return safe_supplier_name(''); }),
        array('safe_product_name', function() { return safe_product_name('منتج تجريبي'); }),
        array('formatMoney', function() { return formatMoney(null); }),
        array('getTreasuryBalance', function() { return getTreasuryBalance(); })
    );
    
    foreach ($function_tests as $test) {
        $total_tests++;
        echo "<h4>اختبار دالة: {$test[0]}</h4>";
        
        try {
            $result = $test[1]();
            echo "<p style='color: green;'>✅ نجح الاختبار - النتيجة: " . htmlspecialchars($result) . "</p>";
            $passed_tests++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ فشل الاختبار - الخطأ: " . $e->getMessage() . "</p>";
            $failed_tests++;
        }
    }
    
    // اختبار قاعدة البيانات
    echo "<h2>5. اختبار قاعدة البيانات</h2>";
    
    $db_tests = array(
        array('اتصال قاعدة البيانات', function() use ($conn) { 
            return $conn && !$conn->connect_error; 
        }),
        array('جدول المبيعات', function() use ($conn) { 
            $result = $conn->query("SELECT COUNT(*) FROM sales"); 
            return $result !== false; 
        }),
        array('جدول المنتجات مع الوحدات', function() use ($conn) { 
            $result = $conn->query("SELECT id, name, unit FROM products LIMIT 1"); 
            return $result !== false; 
        }),
        array('جدول العملاء مع الأرصدة', function() use ($conn) { 
            $result = $conn->query("SELECT id, name, balance FROM customers LIMIT 1"); 
            return $result !== false; 
        }),
        array('جدول الموردين مع الشركات', function() use ($conn) { 
            $result = $conn->query("SELECT id, name, company FROM suppliers LIMIT 1"); 
            return $result !== false; 
        })
    );
    
    foreach ($db_tests as $test) {
        $total_tests++;
        echo "<h4>اختبار: {$test[0]}</h4>";
        
        try {
            $result = $test[1]();
            if ($result) {
                echo "<p style='color: green;'>✅ نجح الاختبار</p>";
                $passed_tests++;
            } else {
                echo "<p style='color: red;'>❌ فشل الاختبار</p>";
                $failed_tests++;
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ فشل الاختبار - الخطأ: " . $e->getMessage() . "</p>";
            $failed_tests++;
        }
    }
    
    // النتائج النهائية
    echo "<hr>";
    echo "<h2>النتائج النهائية</h2>";
    
    $success_rate = $total_tests > 0 ? round(($passed_tests / $total_tests) * 100, 2) : 0;
    
    if ($failed_tests == 0) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 8px; text-align: center;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>🎉 جميع الاختبارات نجحت!</h3>";
        echo "<p style='color: #155724; font-size: 1.1em;'><strong>معدل النجاح: 100%</strong></p>";
        echo "<p style='color: #155724;'>إجمالي الاختبارات: $total_tests | نجح: $passed_tests | فشل: $failed_tests</p>";
        echo "<p style='color: #155724; margin-bottom: 0;'>نظام Zero جاهز للاستخدام التجاري!</p>";
        echo "</div>";
        
        echo "<h3>🚀 النظام جاهز للاستخدام!</h3>";
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;'>";
        
        $final_links = array(
            array('🏠 الصفحة الرئيسية', 'index.php', '#007bff'),
            array('💰 إدارة المبيعات', 'pages/sales/index.php', '#28a745'),
            array('🛒 إدارة المشتريات', 'pages/purchases/index.php', '#17a2b8'),
            array('📦 إدارة المنتجات', 'pages/products/index.php', '#ffc107'),
            array('👥 إدارة العملاء', 'pages/customers/index.php', '#6f42c1'),
            array('📋 إدارة الموردين', 'pages/suppliers/index.php', '#fd7e14')
        );
        
        foreach ($final_links as $link) {
            echo "<div style='text-align: center;'>";
            echo "<a href='{$link[1]}' target='_blank' style='background: {$link[2]}; color: white; padding: 12px 16px; text-decoration: none; border-radius: 8px; display: inline-block; width: 100%; font-weight: bold;'>{$link[0]}</a>";
            echo "</div>";
        }
        
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; border-radius: 8px; text-align: center;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>⚠️ بعض الاختبارات فشلت</h3>";
        echo "<p style='color: #721c24;'><strong>معدل النجاح: $success_rate%</strong></p>";
        echo "<p style='color: #721c24;'>إجمالي الاختبارات: $total_tests | نجح: $passed_tests | فشل: $failed_tests</p>";
        echo "<p style='color: #721c24; margin-bottom: 0;'>يرجى مراجعة الأخطاء وإصلاحها</p>";
        echo "</div>";
    }
    
    echo "<h3>📋 ملفات الإصلاح المتاحة:</h3>";
    echo "<ul>";
    echo "<li><a href='fix-all-missing-columns.php'>إصلاح جميع الأعمدة المفقودة</a></li>";
    echo "<li><a href='fix-htmlspecialchars-errors.php'>إصلاح أخطاء htmlspecialchars</a></li>";
    echo "<li><a href='comprehensive-system-report.php'>التقرير الشامل للنظام</a></li>";
    echo "<li><a href='test-add-pages.php'>اختبار صفحات الإضافة</a></li>";
    echo "<li><a href='test-view-pages.php'>اختبار صفحات العرض</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>❌ خطأ في الفحص</h3>";
    echo "<p>$e->getMessage()</p>";
    echo "</div>";
}
?>

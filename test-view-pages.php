<?php
/**
 * ملف اختبار صفحات العرض
 * Test View Pages
 */

echo "<h1>اختبار صفحات العرض - Test View Pages</h1>";

require_once 'config/db_config.php';

session_start();
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['user_name'] = 'مدير النظام';
$_SESSION['user_role'] = 'admin';
$_SESSION['role'] = 'admin';

try {
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // اختبار صفحات عرض المبيعات
    echo "<h2>اختبار صفحات عرض المبيعات</h2>";
    
    $sales = $conn->query("SELECT id, sale_number FROM sales LIMIT 5");
    if ($sales && $sales->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>رقم الفاتورة</th><th>رابط العرض</th><th>رابط الطباعة</th></tr>";
        
        while ($sale = $sales->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $sale['id'] . "</td>";
            echo "<td>" . $sale['sale_number'] . "</td>";
            echo "<td><a href='pages/sales/view.php?id=" . $sale['id'] . "' target='_blank' style='color: blue;'>عرض الفاتورة</a></td>";
            echo "<td><a href='pages/sales/print.php?id=" . $sale['id'] . "' target='_blank' style='color: green;'>طباعة</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد فواتير مبيعات للاختبار</p>";
    }
    
    // اختبار صفحات عرض المشتريات
    echo "<h2>اختبار صفحات عرض المشتريات</h2>";
    
    $purchases = $conn->query("SELECT id, purchase_number FROM purchases LIMIT 5");
    if ($purchases && $purchases->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>رقم الفاتورة</th><th>رابط العرض</th><th>رابط الطباعة</th></tr>";
        
        while ($purchase = $purchases->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $purchase['id'] . "</td>";
            echo "<td>" . $purchase['purchase_number'] . "</td>";
            echo "<td><a href='pages/purchases/view.php?id=" . $purchase['id'] . "' target='_blank' style='color: blue;'>عرض الفاتورة</a></td>";
            echo "<td><a href='pages/purchases/print.php?id=" . $purchase['id'] . "' target='_blank' style='color: green;'>طباعة</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد فواتير مشتريات للاختبار</p>";
    }
    
    // اختبار صفحات عرض المنتجات
    echo "<h2>اختبار صفحات عرض المنتجات</h2>";
    
    $products = $conn->query("SELECT id, name, barcode FROM products LIMIT 5");
    if ($products && $products->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>اسم المنتج</th><th>الكود</th><th>رابط العرض</th></tr>";
        
        while ($product = $products->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $product['id'] . "</td>";
            echo "<td>" . $product['name'] . "</td>";
            echo "<td>" . ($product['barcode'] ?? '-') . "</td>";
            echo "<td><a href='pages/products/view.php?id=" . $product['id'] . "' target='_blank' style='color: blue;'>عرض المنتج</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد منتجات للاختبار</p>";
    }
    
    // اختبار صفحات عرض العملاء
    echo "<h2>اختبار صفحات عرض العملاء</h2>";
    
    $customers = $conn->query("SELECT id, name, phone FROM customers LIMIT 5");
    if ($customers && $customers->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>اسم العميل</th><th>الهاتف</th><th>رابط العرض</th></tr>";
        
        while ($customer = $customers->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $customer['id'] . "</td>";
            echo "<td>" . $customer['name'] . "</td>";
            echo "<td>" . ($customer['phone'] ?? '-') . "</td>";
            echo "<td><a href='pages/customers/view.php?id=" . $customer['id'] . "' target='_blank' style='color: blue;'>عرض العميل</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد عملاء للاختبار</p>";
    }
    
    // اختبار صفحات عرض الموردين
    echo "<h2>اختبار صفحات عرض الموردين</h2>";
    
    $suppliers = $conn->query("SELECT id, name, phone FROM suppliers LIMIT 5");
    if ($suppliers && $suppliers->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>اسم المورد</th><th>الهاتف</th><th>رابط العرض</th></tr>";
        
        while ($supplier = $suppliers->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $supplier['id'] . "</td>";
            echo "<td>" . $supplier['name'] . "</td>";
            echo "<td>" . ($supplier['phone'] ?? '-') . "</td>";
            echo "<td><a href='pages/suppliers/view.php?id=" . $supplier['id'] . "' target='_blank' style='color: blue;'>عرض المورد</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد موردين للاختبار</p>";
    }
    
    // اختبار تفاصيل المبيعات
    echo "<h2>اختبار تفاصيل المبيعات</h2>";
    
    $sale_items = $conn->query("SELECT si.*, p.name as product_name, p.unit, s.sale_number 
                               FROM sale_items si 
                               LEFT JOIN products p ON si.product_id = p.id 
                               LEFT JOIN sales s ON si.sale_id = s.id 
                               LIMIT 10");
    
    if ($sale_items && $sale_items->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>رقم الفاتورة</th><th>المنتج</th><th>الوحدة</th><th>الكمية</th><th>السعر</th><th>الإجمالي</th>";
        echo "</tr>";
        
        while ($item = $sale_items->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $item['sale_number'] . "</td>";
            echo "<td>" . $item['product_name'] . "</td>";
            echo "<td>" . $item['unit'] . "</td>";
            echo "<td>" . $item['quantity'] . "</td>";
            echo "<td>" . number_format($item['unit_price'], 2) . " ريال</td>";
            echo "<td>" . number_format($item['total_price'], 2) . " ريال</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد تفاصيل مبيعات</p>";
    }
    
    // اختبار الأعمدة المطلوبة
    echo "<h2>فحص الأعمدة المطلوبة</h2>";
    
    $required_columns = array(
        'products' => array('unit', 'sku', 'weight', 'dimensions'),
        'sales' => array('sale_number', 'discount', 'discount_amount', 'payment_method'),
        'purchases' => array('purchase_number', 'discount', 'discount_amount', 'payment_method'),
        'sale_items' => array('discount_amount', 'tax_amount'),
        'purchase_items' => array('discount_amount', 'tax_amount')
    );
    
    foreach ($required_columns as $table => $columns) {
        echo "<h4>جدول $table:</h4>";
        foreach ($columns as $column) {
            $result = $conn->query("SHOW COLUMNS FROM $table LIKE '$column'");
            if ($result && $result->num_rows > 0) {
                echo "<p style='color: green;'>✅ عمود $column موجود</p>";
            } else {
                echo "<p style='color: red;'>❌ عمود $column مفقود</p>";
            }
        }
    }
    
    echo "<hr>";
    echo "<h2>ملخص الاختبار</h2>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>🎉 تم اختبار جميع صفحات العرض!</h3>";
    echo "</div>";
    
    echo "<h3>الصفحات المتاحة للاختبار:</h3>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>";
    
    $test_pages = array(
        array('المبيعات', 'pages/sales/index.php', '💰'),
        array('المشتريات', 'pages/purchases/index.php', '🛒'),
        array('المنتجات', 'pages/products/index.php', '📦'),
        array('العملاء', 'pages/customers/index.php', '👥'),
        array('الموردين', 'pages/suppliers/index.php', '📋'),
        array('المصروفات', 'pages/expenses/index.php', '💸'),
        array('الخزينة', 'pages/treasury/index.php', '🏦'),
        array('الإعدادات', 'pages/settings/index.php', '⚙️')
    );
    
    foreach ($test_pages as $page) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; text-align: center; border-radius: 5px;'>";
        echo "<div style='font-size: 2em;'>{$page[2]}</div>";
        echo "<h4>{$page[0]}</h4>";
        echo "<a href='{$page[1]}' target='_blank' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>فتح الصفحة</a>";
        echo "</div>";
    }
    
    echo "</div>";
    
    echo "<h3>روابط سريعة للاختبار:</h3>";
    echo "<ul>";
    echo "<li><a href='pages/sales/view.php?id=1' target='_blank'>عرض فاتورة مبيعات رقم 1</a></li>";
    echo "<li><a href='pages/purchases/view.php?id=1' target='_blank'>عرض فاتورة مشتريات رقم 1</a></li>";
    echo "<li><a href='pages/products/view.php?id=1' target='_blank'>عرض منتج رقم 1</a></li>";
    echo "<li><a href='pages/customers/view.php?id=1' target='_blank'>عرض عميل رقم 1</a></li>";
    echo "<li><a href='pages/suppliers/view.php?id=1' target='_blank'>عرض مورد رقم 1</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

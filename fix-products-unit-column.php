<?php
/**
 * ملف إصلاح عمود الوحدة في جدول المنتجات
 * Fix Products Unit Column Script
 */

echo "<h1>إصلاح عمود الوحدة في جدول المنتجات - Fix Products Unit Column</h1>";

$db_host = "localhost";
$db_user = "root";
$db_pass = "";
$db_name = "zero";

try {
    // الاتصال بقاعدة البيانات
    $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
    $conn->set_charset("utf8mb4");
    
    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }
    
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // إضافة عمود unit لجدول المنتجات
    echo "<h2>إضافة عمود الوحدة لجدول المنتجات</h2>";
    
    $check_unit = $conn->query("SHOW COLUMNS FROM products LIKE 'unit'");
    
    if (!$check_unit || $check_unit->num_rows == 0) {
        $add_unit = "ALTER TABLE products ADD COLUMN unit VARCHAR(20) DEFAULT 'قطعة' AFTER selling_price";
        
        if ($conn->query($add_unit) === TRUE) {
            echo "<p style='color: green;'>✅ تم إضافة عمود 'unit' لجدول products بنجاح</p>";
        } else {
            echo "<p style='color: red;'>❌ خطأ في إضافة عمود 'unit': " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ️ عمود 'unit' موجود بالفعل في جدول products</p>";
    }
    
    // تحديث وحدات المنتجات الموجودة
    echo "<h2>تحديث وحدات المنتجات</h2>";
    
    $products_units = array(
        'لابتوب' => 'جهاز',
        'ماوس' => 'قطعة',
        'كيبورد' => 'قطعة',
        'شاشة' => 'جهاز',
        'سماعات' => 'زوج',
        'كاميرا' => 'قطعة',
        'هارد' => 'قطعة',
        'فلاشة' => 'قطعة'
    );
    
    foreach ($products_units as $product_keyword => $unit) {
        $update_unit = "UPDATE products SET unit = ? WHERE name LIKE ? AND (unit IS NULL OR unit = 'قطعة')";
        $stmt = $conn->prepare($update_unit);
        $search_term = "%$product_keyword%";
        $stmt->bind_param("ss", $unit, $search_term);
        
        if ($stmt->execute() && $stmt->affected_rows > 0) {
            echo "<p style='color: green;'>✅ تم تحديث وحدة المنتجات التي تحتوي على '$product_keyword' إلى '$unit'</p>";
        }
    }
    
    // إضافة أعمدة أخرى مفقودة قد تكون مطلوبة
    echo "<h2>إضافة أعمدة أخرى مفقودة</h2>";
    
    $additional_columns = array(
        'sku' => "ADD COLUMN sku VARCHAR(50) DEFAULT NULL AFTER barcode",
        'weight' => "ADD COLUMN weight DECIMAL(8,3) DEFAULT NULL AFTER unit",
        'dimensions' => "ADD COLUMN dimensions VARCHAR(100) DEFAULT NULL AFTER weight",
        'warranty_period' => "ADD COLUMN warranty_period INT DEFAULT NULL AFTER dimensions",
        'supplier_id' => "ADD COLUMN supplier_id INT DEFAULT NULL AFTER warranty_period",
        'reorder_level' => "ADD COLUMN reorder_level INT DEFAULT NULL AFTER min_stock",
        'location' => "ADD COLUMN location VARCHAR(100) DEFAULT NULL AFTER reorder_level"
    );
    
    foreach ($additional_columns as $column => $sql) {
        $check = $conn->query("SHOW COLUMNS FROM products LIKE '$column'");
        if (!$check || $check->num_rows == 0) {
            if ($conn->query("ALTER TABLE products $sql") === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column' لجدول products</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ تعذر إضافة عمود '$column': " . $conn->error . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column' موجود بالفعل</p>";
        }
    }
    
    // إضافة أعمدة مفقودة لجدول sale_items
    echo "<h2>فحص جدول تفاصيل المبيعات</h2>";
    
    $sale_items_columns = array(
        'discount_amount' => "ADD COLUMN discount_amount DECIMAL(10,2) DEFAULT 0.00 AFTER total_price",
        'tax_amount' => "ADD COLUMN tax_amount DECIMAL(10,2) DEFAULT 0.00 AFTER discount_amount"
    );
    
    foreach ($sale_items_columns as $column => $sql) {
        $check = $conn->query("SHOW COLUMNS FROM sale_items LIKE '$column'");
        if (!$check || $check->num_rows == 0) {
            if ($conn->query("ALTER TABLE sale_items $sql") === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column' لجدول sale_items</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ تعذر إضافة عمود '$column': " . $conn->error . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column' موجود بالفعل في sale_items</p>";
        }
    }
    
    // إضافة أعمدة مفقودة لجدول purchase_items
    echo "<h2>فحص جدول تفاصيل المشتريات</h2>";
    
    $purchase_items_columns = array(
        'discount_amount' => "ADD COLUMN discount_amount DECIMAL(10,2) DEFAULT 0.00 AFTER total_price",
        'tax_amount' => "ADD COLUMN tax_amount DECIMAL(10,2) DEFAULT 0.00 AFTER discount_amount"
    );
    
    foreach ($purchase_items_columns as $column => $sql) {
        $check = $conn->query("SHOW COLUMNS FROM purchase_items LIKE '$column'");
        if (!$check || $check->num_rows == 0) {
            if ($conn->query("ALTER TABLE purchase_items $sql") === TRUE) {
                echo "<p style='color: green;'>✅ تم إضافة عمود '$column' لجدول purchase_items</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ تعذر إضافة عمود '$column': " . $conn->error . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ عمود '$column' موجود بالفعل في purchase_items</p>";
        }
    }
    
    // عرض بنية جدول المنتجات المحدثة
    echo "<h2>بنية جدول المنتجات المحدثة</h2>";
    $products_structure = $conn->query("DESCRIBE products");
    if ($products_structure) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>اسم العمود</th><th>النوع</th><th>القيمة الافتراضية</th></tr>";
        while ($column = $products_structure->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $column['Field'] . "</td>";
            echo "<td>" . $column['Type'] . "</td>";
            echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // عرض عينة من المنتجات مع الوحدات
    echo "<h2>عينة من المنتجات مع الوحدات</h2>";
    $sample_products = $conn->query("SELECT id, name, barcode, unit, selling_price, stock_quantity FROM products LIMIT 10");
    
    if ($sample_products && $sample_products->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>اسم المنتج</th><th>الكود</th><th>الوحدة</th><th>السعر</th><th>المخزون</th>";
        echo "</tr>";
        
        while ($product = $sample_products->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $product['id'] . "</td>";
            echo "<td>" . $product['name'] . "</td>";
            echo "<td>" . ($product['barcode'] ?? '-') . "</td>";
            echo "<td>" . $product['unit'] . "</td>";
            echo "<td>" . number_format($product['selling_price'], 2) . " ريال</td>";
            echo "<td>" . $product['stock_quantity'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    $conn->close();
    
    echo "<hr>";
    echo "<h2>تم الانتهاء من الإصلاحات</h2>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>🎉 تم إصلاح عمود الوحدة وجميع الأعمدة المفقودة!</h3>";
    echo "</div>";
    
    echo "<h3>يمكنك الآن استخدام جميع صفحات النظام بدون أخطاء:</h3>";
    echo "<ul>";
    echo "<li><a href='pages/sales/view.php?id=1' style='color: blue;'>👁️ عرض فاتورة مبيعات</a></li>";
    echo "<li><a href='pages/sales/index.php' style='color: blue;'>💰 إدارة المبيعات</a></li>";
    echo "<li><a href='pages/products/index.php' style='color: blue;'>📦 إدارة المنتجات</a></li>";
    echo "<li><a href='pages/purchases/view.php?id=1' style='color: blue;'>👁️ عرض فاتورة مشتريات</a></li>";
    echo "<li><a href='pages/purchases/index.php' style='color: blue;'>🛒 إدارة المشتريات</a></li>";
    echo "<li><a href='index.php' style='color: blue;'>🏠 الصفحة الرئيسية</a></li>";
    echo "</ul>";
    
    echo "<h3>الأعمدة المضافة:</h3>";
    echo "<ul>";
    echo "<li><strong>unit:</strong> وحدة المنتج (قطعة، جهاز، زوج، إلخ)</li>";
    echo "<li><strong>sku:</strong> رمز المنتج</li>";
    echo "<li><strong>weight:</strong> وزن المنتج</li>";
    echo "<li><strong>dimensions:</strong> أبعاد المنتج</li>";
    echo "<li><strong>warranty_period:</strong> فترة الضمان</li>";
    echo "<li><strong>supplier_id:</strong> المورد الافتراضي</li>";
    echo "<li><strong>reorder_level:</strong> مستوى إعادة الطلب</li>";
    echo "<li><strong>location:</strong> موقع المنتج في المخزن</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>

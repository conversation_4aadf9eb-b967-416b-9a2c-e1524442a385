<?php
/**
 * صفحة إدارة الخزينة الرئيسية
 * Treasury Management Main Page
 */

// استدعاء ملفات الإعدادات والوظائف
require_once "../../config/db_config.php";
require_once "../../includes/functions.php";

// بدء الجلسة والتحقق من تسجيل الدخول
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION["user_id"])) {
    header("Location: ../../login.php");
    exit();
}

$page_title = "إدارة الخزينة";
$page_icon = "fas fa-money-bill-wave";

// معالجة إضافة حركة مالية جديدة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_transaction'])) {
    try {
        $transaction_type = clean($conn, $_POST['transaction_type']);
        $amount = floatval($_POST['amount']);
        $description = clean($conn, $_POST['description']);
        $transaction_date = clean($conn, $_POST['transaction_date']);
        
        // التحقق من صحة البيانات
        if ($amount <= 0) {
            throw new Exception("المبلغ يجب أن يكون أكبر من صفر");
        }
        
        // الحصول على الرصيد الحالي
        $current_balance = getTreasuryBalance();
        
        // حساب الرصيد الجديد
        if ($transaction_type == 'deposit') {
            $new_balance = $current_balance + $amount;
            $amount_to_record = $amount;
        } else { // withdraw
            if ($current_balance < $amount) {
                throw new Exception("الرصيد غير كافي. الرصيد الحالي: " . formatMoney($current_balance));
            }
            $new_balance = $current_balance - $amount;
            $amount_to_record = -$amount;
        }
        
        // إضافة الحركة
        $insert_query = "INSERT INTO treasury (transaction_type, amount, balance_after, description, user_id, transaction_date) 
                        VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insert_query);
        $stmt->bind_param("sddsss", $transaction_type, $amount_to_record, $new_balance, $description, $_SESSION['user_id'], $transaction_date);
        $stmt->execute();
        
        $_SESSION['success_message'] = "تم إضافة الحركة المالية بنجاح";
        header("Location: index.php");
        exit();
        
    } catch (Exception $e) {
        $_SESSION['error_message'] = "خطأ في إضافة الحركة: " . $e->getMessage();
    }
}

// الحصول على حركات الخزينة مع الفلترة
$search = isset($_GET['search']) ? clean($conn, $_GET['search']) : '';
$date_from = isset($_GET['date_from']) ? clean($conn, $_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? clean($conn, $_GET['date_to']) : '';
$transaction_type = isset($_GET['transaction_type']) ? clean($conn, $_GET['transaction_type']) : '';

$where_conditions = [];
$params = [];
$types = '';

if (!empty($search)) {
    $where_conditions[] = "t.description LIKE ?";
    $search_param = "%$search%";
    $params[] = $search_param;
    $types .= 's';
}

if (!empty($date_from)) {
    $where_conditions[] = "t.transaction_date >= ?";
    $params[] = $date_from;
    $types .= 's';
}

if (!empty($date_to)) {
    $where_conditions[] = "t.transaction_date <= ?";
    $params[] = $date_to;
    $types .= 's';
}

if (!empty($transaction_type)) {
    $where_conditions[] = "t.transaction_type = ?";
    $params[] = $transaction_type;
    $types .= 's';
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
}

// استعلام حركات الخزينة
$query = "SELECT t.*, u.name as user_name 
          FROM treasury t 
          LEFT JOIN users u ON t.user_id = u.id 
          $where_clause 
          ORDER BY t.id DESC";

if (!empty($params)) {
    $stmt = $conn->prepare($query);
    if (!empty($types)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $transactions_result = $stmt->get_result();
} else {
    $transactions_result = $conn->query($query);
}

// حساب الإحصائيات
$stats_query = "SELECT 
                COUNT(*) as total_transactions,
                COALESCE(SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END), 0) as total_income,
                COALESCE(SUM(CASE WHEN amount < 0 THEN ABS(amount) ELSE 0 END), 0) as total_expenses
                FROM treasury t $where_clause";

if (!empty($params)) {
    $stmt = $conn->prepare($stats_query);
    if (!empty($types)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $stats = $stmt->get_result()->fetch_assoc();
} else {
    $stats = $conn->query($stats_query)->fetch_assoc();
}

// الرصيد الحالي
$current_balance = getTreasuryBalance();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - نظام Zero</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="../../assets/css/style.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .main-header {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        .stats-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .stats-card:hover {
            transform: translateY(-5px);
        }
        .table-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .btn-action {
            padding: 0.25rem 0.5rem;
            margin: 0 0.1rem;
            border-radius: 0.375rem;
        }
        .search-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .transaction-income {
            color: #28a745;
            font-weight: bold;
        }
        .transaction-expense {
            color: #dc3545;
            font-weight: bold;
        }
        .balance-card {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    
    <!-- Header -->
    <div class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="mb-0">
                        <i class="<?php echo $page_icon; ?> me-3"></i>
                        <?php echo $page_title; ?>
                    </h1>
                    <p class="mb-0 mt-2">إدارة ومتابعة جميع الحركات المالية</p>
                </div>
                <div class="col-md-6 text-end">
                    <button type="button" class="btn btn-light btn-lg" data-bs-toggle="modal" data-bs-target="#addTransactionModal">
                        <i class="fas fa-plus me-2"></i>
                        حركة مالية جديدة
                    </button>
                    <a href="../../index.php" class="btn btn-outline-light btn-lg ms-2">
                        <i class="fas fa-home me-2"></i>
                        الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        
        <!-- رسائل النجاح والخطأ -->
        <?php if (isset($_SESSION['success_message'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error_message'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>
                <?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- الرصيد الحالي -->
        <div class="balance-card">
            <h2 class="mb-3">
                <i class="fas fa-wallet me-2"></i>
                الرصيد الحالي
            </h2>
            <h1 class="display-4 mb-0"><?php echo formatMoney($current_balance); ?></h1>
        </div>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-warning mb-2">
                            <i class="fas fa-exchange-alt fa-2x"></i>
                        </div>
                        <h3 class="card-title text-warning"><?php echo number_format($stats['total_transactions']); ?></h3>
                        <p class="card-text text-muted">إجمالي الحركات</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-success mb-2">
                            <i class="fas fa-arrow-up fa-2x"></i>
                        </div>
                        <h3 class="card-title text-success"><?php echo formatMoney($stats['total_income']); ?></h3>
                        <p class="card-text text-muted">إجمالي الإيرادات</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-danger mb-2">
                            <i class="fas fa-arrow-down fa-2x"></i>
                        </div>
                        <h3 class="card-title text-danger"><?php echo formatMoney($stats['total_expenses']); ?></h3>
                        <p class="card-text text-muted">إجمالي المصروفات</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stats-card text-center">
                    <div class="card-body">
                        <div class="text-info mb-2">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                        <h3 class="card-title text-info"><?php echo formatMoney($stats['total_income'] - $stats['total_expenses']); ?></h3>
                        <p class="card-text text-muted">صافي الربح</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- البحث والفلترة -->
        <div class="search-container">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label">البحث</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           value="<?php echo htmlspecialchars(search ?? ''); ?>" 
                           placeholder="البحث في الوصف">
                </div>
                <div class="col-md-2">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="<?php echo htmlspecialchars(date_from ?? ''); ?>">
                </div>
                <div class="col-md-2">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="<?php echo htmlspecialchars(date_to ?? ''); ?>">
                </div>
                <div class="col-md-3">
                    <label for="transaction_type" class="form-label">نوع الحركة</label>
                    <select class="form-select" id="transaction_type" name="transaction_type">
                        <option value="">جميع الحركات</option>
                        <option value="sales" <?php echo ($transaction_type == 'sales') ? 'selected' : ''; ?>>مبيعات</option>
                        <option value="purchases" <?php echo ($transaction_type == 'purchases') ? 'selected' : ''; ?>>مشتريات</option>
                        <option value="expenses" <?php echo ($transaction_type == 'expenses') ? 'selected' : ''; ?>>مصروفات</option>
                        <option value="deposit" <?php echo ($transaction_type == 'deposit') ? 'selected' : ''; ?>>إيداع</option>
                        <option value="withdraw" <?php echo ($transaction_type == 'withdraw') ? 'selected' : ''; ?>>سحب</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-search me-1"></i> بحث
                        </button>
                    </div>
                </div>
            </form>
            
            <?php if (!empty($search) || !empty($date_from) || !empty($date_to) || !empty($transaction_type)): ?>
                <div class="mt-3">
                    <a href="index.php" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i> مسح الفلاتر
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- جدول الحركات المالية -->
        <div class="table-container">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th>التاريخ</th>
                            <th>نوع الحركة</th>
                            <th>الوصف</th>
                            <th>المبلغ</th>
                            <th>الرصيد بعد الحركة</th>
                            <th>المستخدم</th>
                            <th>وقت الإنشاء</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ($transactions_result->num_rows > 0): ?>
                            <?php while ($transaction = $transactions_result->fetch_assoc()): ?>
                                <tr>
                                    <td><?php echo date('Y-m-d', strtotime($transaction['transaction_date'])); ?></td>
                                    <td>
                                        <?php
                                        $type_labels = [
                                            'sales' => '<span class="badge bg-success">مبيعات</span>',
                                            'purchases' => '<span class="badge bg-primary">مشتريات</span>',
                                            'expenses' => '<span class="badge bg-danger">مصروفات</span>',
                                            'deposit' => '<span class="badge bg-info">إيداع</span>',
                                            'withdraw' => '<span class="badge bg-warning">سحب</span>'
                                        ];
                                        echo $type_labels[$transaction['transaction_type']] ?? $transaction['transaction_type'];
                                        ?>
                                    </td>
                                    <td><?php echo htmlspecialchars(transaction['description'] ?? ''); ?></td>
                                    <td class="<?php echo ($transaction['amount'] >= 0) ? 'transaction-income' : 'transaction-expense'; ?>">
                                        <?php echo formatMoney(abs($transaction['amount'])); ?>
                                        <?php if ($transaction['amount'] >= 0): ?>
                                            <i class="fas fa-arrow-up ms-1"></i>
                                        <?php else: ?>
                                            <i class="fas fa-arrow-down ms-1"></i>
                                        <?php endif; ?>
                                    </td>
                                    <td class="fw-bold"><?php echo formatMoney($transaction['balance_after']); ?></td>
                                    <td><?php echo htmlspecialchars(transaction['user_name'] ?? ''); ?></td>
                                    <td><?php echo date('Y-m-d H:i', strtotime($transaction['created_at'])); ?></td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد حركات مالية</h5>
                                    <p class="text-muted">لم يتم العثور على أي حركات تطابق معايير البحث</p>
                                    <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#addTransactionModal">
                                        <i class="fas fa-plus me-2"></i>إضافة حركة جديدة
                                    </button>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <!-- Modal إضافة حركة مالية -->
    <div class="modal fade" id="addTransactionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة حركة مالية جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="transaction_type" class="form-label">نوع الحركة <span class="text-danger">*</span></label>
                            <select class="form-select" id="transaction_type" name="transaction_type" required>
                                <option value="">اختر نوع الحركة</option>
                                <option value="deposit">إيداع (إضافة للخزينة)</option>
                                <option value="withdraw">سحب (خصم من الخزينة)</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="amount" class="form-label">المبلغ <span class="text-danger">*</span></label>
                            <input type="number" class="form-control" id="amount" name="amount" 
                                   step="0.01" min="0.01" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="description" name="description" 
                                      rows="3" required placeholder="وصف الحركة المالية"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="transaction_date" class="form-label">تاريخ الحركة <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="transaction_date" name="transaction_date" 
                                   value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        
                        <div class="alert alert-info">
                            <strong>الرصيد الحالي:</strong> <?php echo formatMoney($current_balance); ?>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" name="add_transaction" class="btn btn-warning">
                            <i class="fas fa-save me-2"></i>حفظ الحركة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

</body>
</html>

<?php
/**
 * ملف الاختبار النهائي للنظام
 * Final System Test
 */

echo "<h1>الاختبار النهائي لنظام Zero - Final System Test</h1>";

require_once 'config/db_config.php';
require_once 'includes/functions.php';

$pages_tested = 0;
$pages_working = 0;
$pages_failed = 0;

function testPage($page_name, $page_url, $expected_content = '') {
    global $pages_tested, $pages_working, $pages_failed;
    
    $pages_tested++;
    
    echo "<h3>اختبار صفحة: $page_name</h3>";
    
    // محاولة تضمين الصفحة واختبارها
    ob_start();
    $error_occurred = false;
    
    try {
        // تعيين متغيرات الجلسة للاختبار
        session_start();
        $_SESSION['user_id'] = 1;
        $_SESSION['username'] = 'admin';
        $_SESSION['user_name'] = 'مدير النظام';
        $_SESSION['user_role'] = 'admin';
        $_SESSION['role'] = 'admin';
        
        // محاولة تضمين الصفحة
        if (file_exists($page_url)) {
            include $page_url;
            $content = ob_get_contents();
            
            // التحقق من وجود أخطاء PHP
            if (strpos($content, 'Fatal error') !== false || 
                strpos($content, 'Parse error') !== false ||
                strpos($content, 'mysqli_sql_exception') !== false) {
                $error_occurred = true;
            }
        } else {
            $error_occurred = true;
            echo "<p style='color: red;'>❌ الملف غير موجود</p>";
        }
        
    } catch (Exception $e) {
        $error_occurred = true;
        echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
    }
    
    ob_end_clean();
    
    if (!$error_occurred) {
        echo "<p style='color: green;'>✅ الصفحة تعمل بشكل صحيح</p>";
        echo "<p><a href='$page_url' target='_blank' style='color: blue;'>🔗 فتح الصفحة</a></p>";
        $pages_working++;
    } else {
        echo "<p style='color: red;'>❌ الصفحة بها مشاكل</p>";
        $pages_failed++;
    }
}

// اختبار الصفحات الرئيسية
echo "<h2>اختبار الصفحات الرئيسية</h2>";

testPage("الصفحة الرئيسية", "index.php");
testPage("تسجيل الدخول", "login.php");

// اختبار صفحات المبيعات
echo "<h2>اختبار صفحات المبيعات</h2>";

testPage("قائمة المبيعات", "pages/sales/index.php");

// اختبار صفحات المشتريات
echo "<h2>اختبار صفحات المشتريات</h2>";

testPage("قائمة المشتريات", "pages/purchases/index.php");

// اختبار صفحات المنتجات
echo "<h2>اختبار صفحات المنتجات</h2>";

testPage("قائمة المنتجات", "pages/products/index.php");

// اختبار صفحات العملاء
echo "<h2>اختبار صفحات العملاء</h2>";

testPage("قائمة العملاء", "pages/customers/index.php");

// اختبار صفحات الموردين
echo "<h2>اختبار صفحات الموردين</h2>";

testPage("قائمة الموردين", "pages/suppliers/index.php");

// اختبار صفحات المصروفات
echo "<h2>اختبار صفحات المصروفات</h2>";

testPage("قائمة المصروفات", "pages/expenses/index.php");

// اختبار صفحات الخزينة
echo "<h2>اختبار صفحات الخزينة</h2>";

testPage("إدارة الخزينة", "pages/treasury/index.php");

// اختبار صفحات الإعدادات
echo "<h2>اختبار صفحات الإعدادات</h2>";

testPage("الإعدادات العامة", "pages/settings/index.php");

// اختبار قاعدة البيانات
echo "<h2>اختبار قاعدة البيانات</h2>";

try {
    // اختبار الجداول الأساسية
    $tables_to_check = array(
        'users' => 'المستخدمين',
        'categories' => 'التصنيفات',
        'products' => 'المنتجات',
        'customers' => 'العملاء',
        'suppliers' => 'الموردين',
        'sales' => 'المبيعات',
        'purchases' => 'المشتريات',
        'expenses' => 'المصروفات',
        'treasury_transactions' => 'حركات الخزينة',
        'treasury' => 'الخزينة',
        'settings' => 'الإعدادات'
    );
    
    $tables_ok = 0;
    $tables_missing = 0;
    
    foreach ($tables_to_check as $table => $arabic_name) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✅ جدول $arabic_name ($table) موجود</p>";
            $tables_ok++;
        } else {
            echo "<p style='color: red;'>❌ جدول $arabic_name ($table) مفقود</p>";
            $tables_missing++;
        }
    }
    
    // اختبار الأعمدة المهمة
    echo "<h3>اختبار الأعمدة المهمة</h3>";
    
    $columns_to_check = array(
        'sales' => array('final_amount', 'paid_amount', 'payment_status'),
        'purchases' => array('final_amount', 'paid_amount', 'payment_status'),
        'products' => array('is_active', 'cost_price'),
        'expenses' => array('expense_type'),
        'customers' => array('balance'),
        'suppliers' => array('balance')
    );
    
    $columns_ok = 0;
    $columns_missing = 0;
    
    foreach ($columns_to_check as $table => $columns) {
        foreach ($columns as $column) {
            $result = $conn->query("SHOW COLUMNS FROM $table LIKE '$column'");
            if ($result && $result->num_rows > 0) {
                echo "<p style='color: green;'>✅ عمود $column في جدول $table موجود</p>";
                $columns_ok++;
            } else {
                echo "<p style='color: red;'>❌ عمود $column في جدول $table مفقود</p>";
                $columns_missing++;
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في اختبار قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// النتائج النهائية
echo "<hr>";
echo "<h2>النتائج النهائية</h2>";

$total_pages = $pages_tested;
$success_rate = $total_pages > 0 ? round(($pages_working / $total_pages) * 100, 2) : 0;

echo "<div style='background: " . ($pages_failed == 0 ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($pages_failed == 0 ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3 style='color: " . ($pages_failed == 0 ? '#155724' : '#721c24') . ";'>";

if ($pages_failed == 0) {
    echo "🎉 جميع الصفحات تعمل بشكل مثالي!";
} else {
    echo "⚠️ بعض الصفحات تحتاج إلى إصلاح";
}

echo "</h3>";
echo "<p><strong>إجمالي الصفحات المختبرة:</strong> $total_pages</p>";
echo "<p><strong>تعمل بشكل صحيح:</strong> <span style='color: green;'>$pages_working</span></p>";
echo "<p><strong>بها مشاكل:</strong> <span style='color: red;'>$pages_failed</span></p>";
echo "<p><strong>معدل النجاح:</strong> $success_rate%</p>";
echo "</div>";

if ($pages_failed == 0) {
    echo "<h3>🚀 النظام جاهز للاستخدام الكامل!</h3>";
    
    echo "<h4>الوحدات المتاحة:</h4>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    $modules = array(
        array('المبيعات', 'pages/sales/index.php', '💰', 'إدارة فواتير المبيعات والعملاء'),
        array('المشتريات', 'pages/purchases/index.php', '🛒', 'إدارة فواتير المشتريات والموردين'),
        array('المنتجات', 'pages/products/index.php', '📦', 'إدارة المنتجات والمخزون'),
        array('العملاء', 'pages/customers/index.php', '👥', 'إدارة بيانات العملاء والأرصدة'),
        array('الموردين', 'pages/suppliers/index.php', '📋', 'إدارة بيانات الموردين والأرصدة'),
        array('المصروفات', 'pages/expenses/index.php', '💸', 'تسجيل وإدارة المصروفات'),
        array('الخزينة', 'pages/treasury/index.php', '🏦', 'إدارة الحركات المالية'),
        array('الإعدادات', 'pages/settings/index.php', '⚙️', 'إعدادات النظام والشركة')
    );
    
    foreach ($modules as $module) {
        echo "<div style='border: 1px solid #ddd; padding: 15px; border-radius: 8px; text-align: center;'>";
        echo "<div style='font-size: 2em; margin-bottom: 10px;'>{$module[2]}</div>";
        echo "<h4 style='margin: 10px 0;'>{$module[0]}</h4>";
        echo "<p style='color: #666; font-size: 0.9em; margin: 10px 0;'>{$module[3]}</p>";
        echo "<a href='{$module[1]}' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; display: inline-block;'>فتح الوحدة</a>";
        echo "</div>";
    }
    
    echo "</div>";
    
    echo "<h4>بيانات الدخول:</h4>";
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>";
    echo "<p><strong>اسم المستخدم:</strong> admin</p>";
    echo "<p><strong>كلمة المرور:</strong> admin</p>";
    echo "<p><a href='login.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔐 تسجيل الدخول</a></p>";
    echo "</div>";
    
} else {
    echo "<h3>يرجى إصلاح المشاكل التالية:</h3>";
    echo "<ul>";
    echo "<li><a href='fix-missing-columns.php'>إصلاح الأعمدة المفقودة</a></li>";
    echo "<li><a href='create-tables.php'>إنشاء الجداول المفقودة</a></li>";
    echo "<li><a href='complete-system-fix.php'>الإصلاح الشامل</a></li>";
    echo "</ul>";
}

echo "<hr>";
echo "<h3>ملفات مساعدة:</h3>";
echo "<ul>";
echo "<li><a href='test-all-functions.php'>اختبار جميع الوظائف</a></li>";
echo "<li><a href='database-status.php'>فحص حالة قاعدة البيانات</a></li>";
echo "<li><a href='system-overview.md' target='_blank'>دليل النظام الشامل</a></li>";
echo "</ul>";
?>

<?php
/**
 * ملف الإصلاح الشامل للنظام
 * Complete System Fix
 */

echo "<h1>الإصلاح الشامل لنظام Zero - Complete System Fix</h1>";

$db_host = "localhost";
$db_user = "root";
$db_pass = "";
$db_name = "zero";

$errors = array();
$fixes = array();

try {
    // 1. التحقق من الاتصال بقاعدة البيانات
    echo "<h2>1. فحص الاتصال بقاعدة البيانات</h2>";
    
    $conn = new mysqli($db_host, $db_user, $db_pass, $db_name);
    $conn->set_charset("utf8mb4");
    
    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }
    
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    $fixes[] = "اتصال قاعدة البيانات يعمل بشكل صحيح";
    
    // 2. فحص الجداول المطلوبة
    echo "<h2>2. فحص الجداول المطلوبة</h2>";
    
    $required_tables = array(
        'users' => 'المستخدمين',
        'categories' => 'التصنيفات',
        'products' => 'المنتجات',
        'customers' => 'العملاء',
        'suppliers' => 'الموردين',
        'sales' => 'المبيعات',
        'sale_items' => 'تفاصيل المبيعات',
        'purchases' => 'المشتريات',
        'purchase_items' => 'تفاصيل المشتريات',
        'expenses' => 'المصروفات',
        'treasury_transactions' => 'حركات الخزينة',
        'settings' => 'الإعدادات'
    );
    
    $missing_tables = array();
    
    foreach ($required_tables as $table => $arabic_name) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        
        if ($result && $result->num_rows > 0) {
            echo "<p style='color: green;'>✅ جدول '$table' موجود</p>";
        } else {
            echo "<p style='color: red;'>❌ جدول '$table' مفقود</p>";
            $missing_tables[] = $table;
            $errors[] = "جدول '$table' مفقود";
        }
    }
    
    // 3. فحص المستخدم الافتراضي
    echo "<h2>3. فحص المستخدم الافتراضي</h2>";
    
    if (!in_array('users', $missing_tables)) {
        $result = $conn->query("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
        
        if ($result && $result->num_rows > 0) {
            $row = $result->fetch_assoc();
            if ($row['count'] > 0) {
                echo "<p style='color: green;'>✅ المستخدم الافتراضي 'admin' موجود</p>";
                $fixes[] = "المستخدم الافتراضي موجود";
            } else {
                echo "<p style='color: red;'>❌ المستخدم الافتراضي 'admin' غير موجود</p>";
                $errors[] = "المستخدم الافتراضي مفقود";
            }
        }
    }
    
    // 4. فحص دوال الخزينة
    echo "<h2>4. فحص دوال الخزينة</h2>";
    
    if (!in_array('treasury_transactions', $missing_tables)) {
        // اختبار دالة getTreasuryBalance
        require_once 'includes/functions.php';
        
        try {
            $balance = getTreasuryBalance();
            echo "<p style='color: green;'>✅ دالة getTreasuryBalance تعمل بشكل صحيح - الرصيد: " . number_format($balance, 2) . " ريال</p>";
            $fixes[] = "دوال الخزينة تعمل بشكل صحيح";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ خطأ في دالة getTreasuryBalance: " . $e->getMessage() . "</p>";
            $errors[] = "خطأ في دوال الخزينة";
        }
    }
    
    // 5. فحص الملفات المطلوبة
    echo "<h2>5. فحص الملفات المطلوبة</h2>";
    
    $required_files = array(
        'config/config.php' => 'ملف الإعدادات العامة',
        'config/db_config.php' => 'ملف إعدادات قاعدة البيانات',
        'includes/functions.php' => 'ملف الوظائف المساعدة',
        'includes/header.php' => 'ملف رأس الصفحة',
        'includes/footer.php' => 'ملف تذييل الصفحة',
        'login.php' => 'صفحة تسجيل الدخول',
        'index.php' => 'الصفحة الرئيسية'
    );
    
    foreach ($required_files as $file => $description) {
        if (file_exists($file)) {
            echo "<p style='color: green;'>✅ $description موجود</p>";
        } else {
            echo "<p style='color: red;'>❌ $description مفقود</p>";
            $errors[] = "$description مفقود";
        }
    }
    
    // 6. فحص مجلدات الرفع
    echo "<h2>6. فحص مجلدات الرفع</h2>";
    
    $upload_dirs = array(
        'uploads' => 'مجلد الرفع الرئيسي',
        'uploads/logo' => 'مجلد شعارات الشركة',
        'uploads/products' => 'مجلد صور المنتجات'
    );
    
    foreach ($upload_dirs as $dir => $description) {
        if (is_dir($dir)) {
            if (is_writable($dir)) {
                echo "<p style='color: green;'>✅ $description موجود وقابل للكتابة</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ $description موجود لكن غير قابل للكتابة</p>";
                $errors[] = "$description غير قابل للكتابة";
            }
        } else {
            echo "<p style='color: red;'>❌ $description غير موجود</p>";
            $errors[] = "$description مفقود";
            
            // محاولة إنشاء المجلد
            if (mkdir($dir, 0755, true)) {
                echo "<p style='color: green;'>✅ تم إنشاء $description</p>";
                $fixes[] = "تم إنشاء $description";
            } else {
                echo "<p style='color: red;'>❌ فشل في إنشاء $description</p>";
            }
        }
    }
    
    $conn->close();
    
    // 7. تقرير النتائج
    echo "<hr>";
    echo "<h2>تقرير النتائج النهائي</h2>";
    
    if (empty($errors)) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h3 style='color: #155724;'>🎉 النظام جاهز للاستخدام!</h3>";
        echo "<p>جميع الفحوصات نجحت والنظام يعمل بشكل مثالي.</p>";
        echo "</div>";
        
        echo "<h3>الإصلاحات المطبقة:</h3>";
        echo "<ul>";
        foreach ($fixes as $fix) {
            echo "<li style='color: green;'>✅ $fix</li>";
        }
        echo "</ul>";
        
        echo "<h3>روابط النظام:</h3>";
        echo "<ul>";
        echo "<li><a href='login.php' style='color: blue;'>🔐 تسجيل الدخول</a></li>";
        echo "<li><a href='index.php' style='color: blue;'>🏠 الصفحة الرئيسية</a></li>";
        echo "<li><a href='test-connection.php' style='color: blue;'>🧪 اختبار النظام</a></li>";
        echo "</ul>";
        
        echo "<h4>بيانات الدخول:</h4>";
        echo "<ul>";
        echo "<li><strong>اسم المستخدم:</strong> admin</li>";
        echo "<li><strong>كلمة المرور:</strong> admin</li>";
        echo "</ul>";
        
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
        echo "<h3 style='color: #721c24;'>⚠️ يوجد مشاكل تحتاج إلى إصلاح</h3>";
        echo "</div>";
        
        echo "<h3>المشاكل المكتشفة:</h3>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li style='color: red;'>❌ $error</li>";
        }
        echo "</ul>";
        
        echo "<h3>الحلول المقترحة:</h3>";
        echo "<ul>";
        if (!empty($missing_tables)) {
            echo "<li><a href='create-tables.php' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>إنشاء الجداول المفقودة</a></li>";
        }
        echo "<li><a href='database-status.php' style='background: #6c757d; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>فحص قاعدة البيانات</a></li>";
        echo "<li><a href='fix-treasury-functions.php' style='background: #28a745; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>إصلاح دوال الخزينة</a></li>";
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>❌ خطأ في النظام</h3>";
    echo "<p>$e->getMessage()</p>";
    echo "</div>";
    
    echo "<h3>خطوات استكشاف الأخطاء:</h3>";
    echo "<ol>";
    echo "<li>تأكد من تشغيل WAMP وأن الأيقونة خضراء</li>";
    echo "<li>تأكد من تشغيل خدمة MySQL</li>";
    echo "<li><a href='create-database.php'>إنشاء قاعدة البيانات</a></li>";
    echo "<li><a href='create-tables.php'>إنشاء الجداول</a></li>";
    echo "</ol>";
}
?>

<?php
/**
 * ملف الوظائف المساعدة
 * Helper Functions
 */

// استيراد ملف الإعدادات
require_once __DIR__ . "/../config/db_config.php";

/**
 * دالة للحصول على إعدادات النظام
 * @param string $key مفتاح الإعداد
 * @param mixed $default القيمة الافتراضية إذا لم يوجد الإعداد
 * @return mixed قيمة الإعداد
 */
function getSetting($key, $default = null) {
    global $conn;

    $key = clean($conn, $key);
    $query = "SELECT setting_value FROM settings WHERE setting_key = '$key' LIMIT 1";
    $result = $conn->query($query);

    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return $row['setting_value'];
    }

    return $default;
}

/**
 * دالة لتحديث إعدادات النظام
 * @param string $key مفتاح الإعداد
 * @param mixed $value قيمة الإعداد
 * @return bool نتيجة التحديث
 */
function updateSetting($key, $value) {
    global $conn;

    $key = clean($conn, $key);
    $value = clean($conn, $value);

    $query = "UPDATE settings SET setting_value = '$value' WHERE setting_key = '$key'";
    return $conn->query($query);
}

/**
 * دالة لحفظ إعدادات النظام (إضافة أو تحديث)
 * @param string $key مفتاح الإعداد
 * @param mixed $value قيمة الإعداد
 * @return bool نتيجة العملية
 */
function saveSetting($key, $value) {
    global $conn;

    $key = clean($conn, $key);
    $value = clean($conn, $value);

    // التحقق من وجود الإعداد
    $check_query = "SELECT id FROM settings WHERE setting_key = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("s", $key);
    $check_stmt->execute();
    $exists = $check_stmt->get_result()->num_rows > 0;

    if ($exists) {
        // تحديث الإعداد الموجود
        $update_query = "UPDATE settings SET setting_value = ? WHERE setting_key = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("ss", $value, $key);
        return $update_stmt->execute();
    } else {
        // إضافة إعداد جديد
        $insert_query = "INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)";
        $insert_stmt = $conn->prepare($insert_query);
        $insert_stmt->bind_param("ss", $key, $value);
        return $insert_stmt->execute();
    }
}

/**
 * دالة لتنسيق المبالغ المالية
 * @param float $amount المبلغ
 * @param bool $showCurrency إظهار رمز العملة
 * @return string المبلغ المنسق
 */
function formatMoney($amount, $showCurrency = true) {
    // التعامل مع القيم الفارغة أو null
    if ($amount === null || $amount === '') {
        $amount = 0;
    }

    $amount = number_format((float)$amount, 2, '.', ',');
    if ($showCurrency) {
        $currency = getSetting('store_currency', 'ريال');
        return $amount . ' ' . $currency;
    }
    return $amount;
}

/**
 * دالة لتنسيق التاريخ
 * @param string $date التاريخ
 * @param string $format صيغة التاريخ
 * @return string التاريخ المنسق
 */
function formatDate($date, $format = 'd/m/Y') {
    return date($format, strtotime($date));
}

/**
 * دالة للحصول على رصيد الخزينة
 * @return float رصيد الخزينة
 */
function getTreasuryBalance() {
    global $conn;

    // حساب الرصيد من مجموع الإيرادات والمصروفات
    $query = "SELECT
                SUM(CASE WHEN type = 'income' THEN amount ELSE 0 END) as total_income,
                SUM(CASE WHEN type = 'expense' THEN amount ELSE 0 END) as total_expense
              FROM treasury_transactions";

    $result = $conn->query($query);

    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $total_income = (float) ($row['total_income'] ?? 0);
        $total_expense = (float) ($row['total_expense'] ?? 0);
        return $total_income - $total_expense;
    }

    return 0;
}

/**
 * دالة لإضافة حركة مالية في الخزينة
 * @param string $type نوع الحركة
 * @param int $referenceId معرف المرجع (اختياري)
 * @param float $amount المبلغ
 * @param string $description وصف الحركة
 * @param int $userId معرف المستخدم
 * @param string $date تاريخ الحركة (اختياري)
 * @return bool نتيجة العملية
 */
function addTreasuryTransaction($type, $referenceId, $amount, $description, $userId, $date = null) {
    global $conn;

    try {
        // التحقق من صحة البيانات
        if (empty($type) || $amount == 0 || empty($description) || empty($userId)) {
            throw new Exception("بيانات غير صحيحة");
        }

        // الحصول على الرصيد الحالي
        $currentBalance = getTreasuryBalance();

        // حساب الرصيد الجديد والمبلغ المسجل
        $newBalance = $currentBalance;
        $recordedAmount = $amount;

        if ($type == 'sales' || $type == 'deposit') {
            $newBalance += abs($amount);
            $recordedAmount = abs($amount);
        } else if ($type == 'purchases' || $type == 'expenses' || $type == 'withdraw') {
            // التحقق من كفاية الرصيد للمصروفات والسحوبات
            if (($type == 'expenses' || $type == 'withdraw') && $currentBalance < abs($amount)) {
                throw new Exception("الرصيد غير كافي. الرصيد الحالي: " . formatMoney($currentBalance));
            }
            $newBalance -= abs($amount);
            $recordedAmount = -abs($amount);
        }

        // تنظيف البيانات
        $type = clean($conn, $type);
        $referenceId = $referenceId ? (int) $referenceId : null;
        $description = clean($conn, $description);
        $userId = (int) $userId;
        $transactionDate = $date ? $date : date('Y-m-d');

        // تحديد نوع الحركة (income أو expense)
        $transaction_type = 'income';
        if ($type == 'purchases' || $type == 'expenses' || $type == 'withdraw') {
            $transaction_type = 'expense';
        }

        // إضافة الحركة باستخدام Prepared Statement
        $query = "INSERT INTO treasury_transactions (type, amount, description, reference_type, reference_id, user_id, transaction_date)
                  VALUES (?, ?, ?, ?, ?, ?, ?)";

        $stmt = $conn->prepare($query);
        $stmt->bind_param("sdssiis", $transaction_type, $recordedAmount, $description, $type, $referenceId, $userId, $transactionDate);

        return $stmt->execute();

    } catch (Exception $e) {
        error_log("Treasury Transaction Error: " . $e->getMessage());
        return false;
    }
}

/**
 * دالة لحذف حركة مالية من الخزينة
 * @param string $type نوع الحركة
 * @param int $referenceId معرف المرجع
 * @return bool نتيجة العملية
 */
function deleteTreasuryTransaction($type, $referenceId) {
    global $conn;

    try {
        $type = clean($conn, $type);
        $referenceId = (int) $referenceId;

        $query = "DELETE FROM treasury_transactions WHERE reference_type = ? AND reference_id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("si", $type, $referenceId);

        return $stmt->execute();

    } catch (Exception $e) {
        error_log("Delete Treasury Transaction Error: " . $e->getMessage());
        return false;
    }
}

/**
 * دالة لتحديث حركة مالية في الخزينة
 * @param string $type نوع الحركة
 * @param int $referenceId معرف المرجع
 * @param float $newAmount المبلغ الجديد
 * @param string $description الوصف الجديد
 * @param int $userId معرف المستخدم
 * @param string $date تاريخ الحركة
 * @return bool نتيجة العملية
 */
function updateTreasuryTransaction($type, $referenceId, $newAmount, $description, $userId, $date = null) {
    global $conn;

    try {
        // حذف الحركة القديمة
        deleteTreasuryTransaction($type, $referenceId);

        // إضافة الحركة الجديدة
        return addTreasuryTransaction($type, $referenceId, $newAmount, $description, $userId, $date);

    } catch (Exception $e) {
        error_log("Update Treasury Transaction Error: " . $e->getMessage());
        return false;
    }
}

/**
 * دالة للحصول على قائمة المنتجات
 * @param int $limit عدد النتائج
 * @param int $offset بداية النتائج
 * @param string $search نص البحث
 * @return array مصفوفة من المنتجات
 */
function getProducts($limit = 10, $offset = 0, $search = '') {
    global $conn;

    $limit = (int) $limit;
    $offset = (int) $offset;

    $whereClause = '';
    if (!empty($search)) {
        $search = clean($conn, $search);
        $whereClause = "WHERE p.name LIKE '%$search%' OR p.code LIKE '%$search%'";
    }

    $query = "SELECT p.*, c.name AS category_name 
              FROM products p 
              LEFT JOIN categories c ON p.category_id = c.id 
              $whereClause 
              ORDER BY p.id DESC 
              LIMIT $limit OFFSET $offset";

    $result = $conn->query($query);

    $products = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $products[] = $row;
        }
    }

    return $products;
}

/**
 * دالة للحصول على معلومات منتج معين
 * @param int $productId معرف المنتج
 * @return array|bool معلومات المنتج أو false إذا لم يوجد
 */
function getProductById($productId) {
    global $conn;

    $productId = (int) $productId;

    $query = "SELECT p.*, c.name AS category_name 
              FROM products p 
              LEFT JOIN categories c ON p.category_id = c.id 
              WHERE p.id = $productId";

    $result = $conn->query($query);

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return false;
}

/**
 * دالة للتحقق من وجود كمية كافية من المنتج
 * @param int $productId معرف المنتج
 * @param int $quantity الكمية المطلوبة
 * @return bool نتيجة التحقق
 */
function checkProductQuantity($productId, $quantity) {
    $product = getProductById($productId);

    if ($product && $product['quantity'] >= $quantity) {
        return true;
    }

    return false;
}

/**
 * دالة لتحديث كمية المنتج
 * @param int $productId معرف المنتج
 * @param int $quantity الكمية الجديدة (موجبة للإضافة، سالبة للخصم)
 * @return bool نتيجة التحديث
 */
function updateProductQuantity($productId, $quantity) {
    global $conn;

    $productId = (int) $productId;
    $quantity = (int) $quantity;

    // تحديث الكمية
    $query = "UPDATE products SET quantity = quantity + ($quantity) WHERE id = $productId";
    return $conn->query($query);
}

/**
 * دالة للحصول على قائمة العملاء
 * @param int $limit عدد النتائج
 * @param int $offset بداية النتائج
 * @param string $search نص البحث
 * @return array مصفوفة من العملاء
 */
function getCustomers($limit = 10, $offset = 0, $search = '') {
    global $conn;

    $limit = (int) $limit;
    $offset = (int) $offset;

    $whereClause = '';
    if (!empty($search)) {
        $search = clean($conn, $search);
        $whereClause = "WHERE name LIKE '%$search%' OR phone LIKE '%$search%' OR email LIKE '%$search%'";
    }

    $query = "SELECT * FROM customers $whereClause ORDER BY id DESC LIMIT $limit OFFSET $offset";

    $result = $conn->query($query);

    $customers = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $customers[] = $row;
        }
    }

    return $customers;
}

/**
 * دالة للحصول على معلومات عميل معين
 * @param int $customerId معرف العميل
 * @return array|bool معلومات العميل أو false إذا لم يوجد
 */
function getCustomerById($customerId) {
    global $conn;

    $customerId = (int) $customerId;

    $query = "SELECT * FROM customers WHERE id = $customerId";

    $result = $conn->query($query);

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return false;
}

/**
 * دالة للحصول على قائمة الموردين
 * @param int $limit عدد النتائج
 * @param int $offset بداية النتائج
 * @param string $search نص البحث
 * @return array مصفوفة من الموردين
 */
function getSuppliers($limit = 10, $offset = 0, $search = '') {
    global $conn;

    $limit = (int) $limit;
    $offset = (int) $offset;

    $whereClause = '';
    if (!empty($search)) {
        $search = clean($conn, $search);
        $whereClause = "WHERE name LIKE '%$search%' OR phone LIKE '%$search%' OR email LIKE '%$search%'";
    }

    $query = "SELECT * FROM suppliers $whereClause ORDER BY id DESC LIMIT $limit OFFSET $offset";

    $result = $conn->query($query);

    $suppliers = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $suppliers[] = $row;
        }
    }

    return $suppliers;
}

/**
 * دالة للحصول على معلومات مورد معين
 * @param int $supplierId معرف المورد
 * @return array|bool معلومات المورد أو false إذا لم يوجد
 */
function getSupplierById($supplierId) {
    global $conn;

    $supplierId = (int) $supplierId;

    $query = "SELECT * FROM suppliers WHERE id = $supplierId";

    $result = $conn->query($query);

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return false;
}

/**
 * دالة للحصول على فاتورة مبيعات
 * @param int $saleId معرف الفاتورة
 * @return array|bool معلومات الفاتورة أو false إذا لم يوجد
 */
function getSaleById($saleId) {
    global $conn;

    $saleId = (int) $saleId;

    $query = "SELECT s.*, c.name AS customer_name, u.name AS user_name 
              FROM sales s 
              LEFT JOIN customers c ON s.customer_id = c.id 
              LEFT JOIN users u ON s.user_id = u.id 
              WHERE s.id = $saleId";

    $result = $conn->query($query);

    if ($result && $result->num_rows > 0) {
        $sale = $result->fetch_assoc();

        // الحصول على عناصر الفاتورة
        $query = "SELECT si.*, p.name AS product_name, p.code AS product_code 
                  FROM sale_items si 
                  LEFT JOIN products p ON si.product_id = p.id 
                  WHERE si.sale_id = $saleId";

        $result = $conn->query($query);

        $items = [];
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $items[] = $row;
            }
        }

        $sale['items'] = $items;

        return $sale;
    }

    return false;
}

/**
 * دالة للحصول على فاتورة مشتريات
 * @param int $purchaseId معرف الفاتورة
 * @return array|bool معلومات الفاتورة أو false إذا لم يوجد
 */
function getPurchaseById($purchaseId) {
    global $conn;

    $purchaseId = (int) $purchaseId;

    $query = "SELECT p.*, s.name AS supplier_name, u.name AS user_name 
              FROM purchases p 
              LEFT JOIN suppliers s ON p.supplier_id = s.id 
              LEFT JOIN users u ON p.user_id = u.id 
              WHERE p.id = $purchaseId";

    $result = $conn->query($query);

    if ($result && $result->num_rows > 0) {
        $purchase = $result->fetch_assoc();

        // الحصول على عناصر الفاتورة
        $query = "SELECT pi.*, p.name AS product_name, p.code AS product_code 
                  FROM purchase_items pi 
                  LEFT JOIN products p ON pi.product_id = p.id 
                  WHERE pi.purchase_id = $purchaseId";

        $result = $conn->query($query);

        $items = [];
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $items[] = $row;
            }
        }

        $purchase['items'] = $items;

        return $purchase;
    }

    return false;
}

/**
 * دالة لإنشاء رقم فاتورة جديد
 * @param string $prefix بادئة رقم الفاتورة
 * @return string رقم الفاتورة
 */
function generateInvoiceNumber($prefix = 'INV') {
    $date = date('Ymd');
    $random = mt_rand(1000, 9999);
    return $prefix . '-' . $date . '-' . $random;
}

/**
 * توليد رقم مبيعة جديد
 * @param object $conn اتصال قاعدة البيانات
 * @return string رقم المبيعة
 */
function generateSaleNumber($conn) {
    $today = date('Ymd');
    $query = "SELECT COUNT(*) as count FROM sales WHERE DATE(created_at) = CURDATE()";
    $result = $conn->query($query);
    $count = $result->fetch_assoc()['count'] + 1;
    return "S" . $today . str_pad($count, 4, '0', STR_PAD_LEFT);
}

/**
 * توليد رقم مشتريات جديد
 * @param object $conn اتصال قاعدة البيانات
 * @return string رقم المشتريات
 */
function generatePurchaseNumber($conn) {
    $today = date('Ymd');
    $query = "SELECT COUNT(*) as count FROM purchases WHERE DATE(created_at) = CURDATE()";
    $result = $conn->query($query);
    $count = $result->fetch_assoc()['count'] + 1;
    return "P" . $today . str_pad($count, 4, '0', STR_PAD_LEFT);
}

/**
 * توليد رقم مصروف جديد
 * @param object $conn اتصال قاعدة البيانات
 * @return string رقم المصروف
 */
function generateExpenseNumber($conn) {
    $today = date('Ymd');
    $query = "SELECT COUNT(*) as count FROM expenses WHERE DATE(created_at) = CURDATE()";
    $result = $conn->query($query);
    $count = $result->fetch_assoc()['count'] + 1;
    return "E" . $today . str_pad($count, 4, '0', STR_PAD_LEFT);
}

/**
 * دالة لتحديث رصيد العميل
 * @param int $customerId معرف العميل
 * @param float $amount المبلغ (موجب للإضافة، سالب للخصم)
 * @param string $description وصف العملية
 * @return bool نتيجة العملية
 */
function updateCustomerBalance($customerId, $amount, $description = '') {
    global $conn;

    try {
        $customerId = (int) $customerId;
        $amount = (float) $amount;

        $query = "UPDATE customers SET balance = balance + ? WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("di", $amount, $customerId);

        return $stmt->execute();

    } catch (Exception $e) {
        error_log("Update Customer Balance Error: " . $e->getMessage());
        return false;
    }
}

/**
 * دالة لتحديث رصيد المورد
 * @param int $supplierId معرف المورد
 * @param float $amount المبلغ (موجب للإضافة، سالب للخصم)
 * @param string $description وصف العملية
 * @return bool نتيجة العملية
 */
function updateSupplierBalance($supplierId, $amount, $description = '') {
    global $conn;

    try {
        $supplierId = (int) $supplierId;
        $amount = (float) $amount;

        $query = "UPDATE suppliers SET balance = balance + ? WHERE id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("di", $amount, $supplierId);

        return $stmt->execute();

    } catch (Exception $e) {
        error_log("Update Supplier Balance Error: " . $e->getMessage());
        return false;
    }
}

/**
 * دالة للحصول على رصيد العميل
 * @param int $customerId معرف العميل
 * @return float رصيد العميل
 */
function getCustomerBalance($customerId) {
    global $conn;

    $customerId = (int) $customerId;
    $query = "SELECT balance FROM customers WHERE id = ? LIMIT 1";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $customerId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return (float) $row['balance'];
    }

    return 0.0;
}

/**
 * دالة للحصول على رصيد المورد
 * @param int $supplierId معرف المورد
 * @return float رصيد المورد
 */
function getSupplierBalance($supplierId) {
    global $conn;

    $supplierId = (int) $supplierId;
    $query = "SELECT balance FROM suppliers WHERE id = ? LIMIT 1";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $supplierId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        return (float) $row['balance'];
    }

    return 0.0;
}
?>
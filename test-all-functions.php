<?php
/**
 * ملف اختبار جميع وظائف النظام
 * Test All System Functions
 */

echo "<h1>اختبار جميع وظائف النظام - Test All System Functions</h1>";

require_once 'config/db_config.php';
require_once 'includes/functions.php';

$tests_passed = 0;
$tests_failed = 0;

function runTest($test_name, $test_function) {
    global $tests_passed, $tests_failed;
    
    echo "<h3>اختبار: $test_name</h3>";
    
    try {
        $result = $test_function();
        if ($result) {
            echo "<p style='color: green;'>✅ نجح الاختبار</p>";
            $tests_passed++;
        } else {
            echo "<p style='color: red;'>❌ فشل الاختبار</p>";
            $tests_failed++;
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ خطأ في الاختبار: " . $e->getMessage() . "</p>";
        $tests_failed++;
    }
}

// اختبار الاتصال بقاعدة البيانات
runTest("الاتصال بقاعدة البيانات", function() {
    global $conn;
    return $conn && !$conn->connect_error;
});

// اختبار دالة getTreasuryBalance
runTest("دالة رصيد الخزينة", function() {
    $balance = getTreasuryBalance();
    return is_numeric($balance);
});

// اختبار دالة formatMoney
runTest("دالة تنسيق المبالغ", function() {
    $formatted = formatMoney(1234.56);
    return !empty($formatted) && strpos($formatted, '1,234.56') !== false;
});

// اختبار دالة formatDate
runTest("دالة تنسيق التاريخ", function() {
    $formatted = formatDate('2024-01-15');
    return !empty($formatted);
});

// اختبار دالة clean
runTest("دالة تنظيف البيانات", function() {
    global $conn;
    $cleaned = clean($conn, "<script>alert('test')</script>");
    return strpos($cleaned, '<script>') === false;
});

// اختبار دالة generateInvoiceNumber
runTest("دالة توليد رقم الفاتورة", function() {
    $invoice_number = generateInvoiceNumber();
    return !empty($invoice_number) && strpos($invoice_number, 'INV-') === 0;
});

// اختبار دالة generateExpenseNumber
runTest("دالة توليد رقم المصروف", function() {
    $expense_number = generateExpenseNumber();
    return !empty($expense_number) && strpos($expense_number, 'E') === 0;
});

// اختبار وجود الجداول
runTest("فحص الجداول المطلوبة", function() {
    global $conn;
    
    $required_tables = array(
        'users', 'categories', 'products', 'customers', 'suppliers',
        'sales', 'sale_items', 'purchases', 'purchase_items',
        'expenses', 'treasury_transactions', 'settings'
    );
    
    foreach ($required_tables as $table) {
        $result = $conn->query("SHOW TABLES LIKE '$table'");
        if (!$result || $result->num_rows == 0) {
            return false;
        }
    }
    
    return true;
});

// اختبار أعمدة الرصيد
runTest("فحص أعمدة الرصيد", function() {
    global $conn;
    
    // فحص عمود balance في جدول suppliers
    $suppliers_balance = $conn->query("SHOW COLUMNS FROM suppliers LIKE 'balance'");
    if (!$suppliers_balance || $suppliers_balance->num_rows == 0) {
        return false;
    }
    
    // فحص عمود balance في جدول customers
    $customers_balance = $conn->query("SHOW COLUMNS FROM customers LIKE 'balance'");
    if (!$customers_balance || $customers_balance->num_rows == 0) {
        return false;
    }
    
    return true;
});

// اختبار دوال الأرصدة
runTest("دوال إدارة الأرصدة", function() {
    // اختبار دالة getCustomerBalance
    $customer_balance = getCustomerBalance(1);
    if (!is_numeric($customer_balance)) {
        return false;
    }
    
    // اختبار دالة getSupplierBalance
    $supplier_balance = getSupplierBalance(1);
    if (!is_numeric($supplier_balance)) {
        return false;
    }
    
    return true;
});

// اختبار المستخدم الافتراضي
runTest("المستخدم الافتراضي", function() {
    global $conn;
    
    $result = $conn->query("SELECT * FROM users WHERE username = 'admin' LIMIT 1");
    return $result && $result->num_rows > 0;
});

// اختبار البيانات التجريبية
runTest("البيانات التجريبية", function() {
    global $conn;
    
    // فحص وجود موردين
    $suppliers = $conn->query("SELECT COUNT(*) as count FROM suppliers")->fetch_assoc();
    if ($suppliers['count'] == 0) {
        return false;
    }
    
    // فحص وجود عملاء
    $customers = $conn->query("SELECT COUNT(*) as count FROM customers")->fetch_assoc();
    if ($customers['count'] == 0) {
        return false;
    }
    
    return true;
});

// اختبار الإعدادات
runTest("إعدادات النظام", function() {
    $company_name = getSetting('company_name', 'غير محدد');
    return !empty($company_name);
});

// اختبار مجلدات الرفع
runTest("مجلدات الرفع", function() {
    $upload_dirs = array('uploads', 'uploads/logo', 'uploads/products');
    
    foreach ($upload_dirs as $dir) {
        if (!is_dir($dir)) {
            return false;
        }
    }
    
    return true;
});

// عرض النتائج النهائية
echo "<hr>";
echo "<h2>نتائج الاختبارات</h2>";

$total_tests = $tests_passed + $tests_failed;
$success_rate = $total_tests > 0 ? round(($tests_passed / $total_tests) * 100, 2) : 0;

echo "<div style='background: " . ($tests_failed == 0 ? '#d4edda' : '#f8d7da') . "; border: 1px solid " . ($tests_failed == 0 ? '#c3e6cb' : '#f5c6cb') . "; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3 style='color: " . ($tests_failed == 0 ? '#155724' : '#721c24') . ";'>";

if ($tests_failed == 0) {
    echo "🎉 جميع الاختبارات نجحت!";
} else {
    echo "⚠️ بعض الاختبارات فشلت";
}

echo "</h3>";
echo "<p><strong>إجمالي الاختبارات:</strong> $total_tests</p>";
echo "<p><strong>نجح:</strong> <span style='color: green;'>$tests_passed</span></p>";
echo "<p><strong>فشل:</strong> <span style='color: red;'>$tests_failed</span></p>";
echo "<p><strong>معدل النجاح:</strong> $success_rate%</p>";
echo "</div>";

if ($tests_failed == 0) {
    echo "<h3>النظام جاهز للاستخدام! 🚀</h3>";
    echo "<ul>";
    echo "<li><a href='login.php' style='color: blue;'>🔐 تسجيل الدخول</a></li>";
    echo "<li><a href='index.php' style='color: blue;'>🏠 الصفحة الرئيسية</a></li>";
    echo "<li><a href='pages/suppliers/index.php' style='color: blue;'>📋 إدارة الموردين</a></li>";
    echo "<li><a href='pages/customers/index.php' style='color: blue;'>👥 إدارة العملاء</a></li>";
    echo "<li><a href='pages/products/index.php' style='color: blue;'>📦 إدارة المنتجات</a></li>";
    echo "</ul>";
    
    echo "<h4>بيانات الدخول:</h4>";
    echo "<ul>";
    echo "<li><strong>اسم المستخدم:</strong> admin</li>";
    echo "<li><strong>كلمة المرور:</strong> admin</li>";
    echo "</ul>";
} else {
    echo "<h3>يرجى إصلاح المشاكل التالية:</h3>";
    echo "<ul>";
    echo "<li><a href='create-database.php'>إنشاء قاعدة البيانات</a></li>";
    echo "<li><a href='create-tables.php'>إنشاء الجداول</a></li>";
    echo "<li><a href='add-balance-columns.php'>إضافة أعمدة الرصيد</a></li>";
    echo "<li><a href='fix-treasury-functions.php'>إصلاح دوال الخزينة</a></li>";
    echo "<li><a href='complete-system-fix.php'>الإصلاح الشامل</a></li>";
    echo "</ul>";
}

echo "<hr>";
echo "<h3>ملفات مساعدة أخرى:</h3>";
echo "<ul>";
echo "<li><a href='database-status.php'>فحص حالة قاعدة البيانات</a></li>";
echo "<li><a href='test-connection.php'>اختبار الاتصال</a></li>";
echo "<li><a href='system-overview.md' target='_blank'>دليل النظام</a></li>";
echo "<li><a href='quick-setup-guide.md' target='_blank'>دليل الإعداد السريع</a></li>";
echo "</ul>";
?>

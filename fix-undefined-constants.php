<?php
/**
 * ملف إصلاح أخطاء الثوابت غير المعرفة
 * Fix Undefined Constants Errors
 */

echo "<h1>إصلاح أخطاء الثوابت غير المعرفة - Fix Undefined Constants</h1>";

try {
    echo "<p style='color: green;'>✅ بدء عملية إصلاح أخطاء الثوابت غير المعرفة</p>";
    
    // قائمة الملفات التي قد تحتوي على أخطاء
    $files_to_check = array(
        'pages/treasury/index.php',
        'pages/sales/view.php',
        'pages/sales/index.php',
        'pages/purchases/view.php',
        'pages/purchases/index.php',
        'pages/products/view.php',
        'pages/products/index.php',
        'pages/customers/view.php',
        'pages/customers/index.php',
        'pages/suppliers/view.php',
        'pages/suppliers/index.php',
        'pages/expenses/view.php',
        'pages/expenses/index.php'
    );
    
    $fixes_applied = 0;
    $files_processed = 0;
    
    foreach ($files_to_check as $file_path) {
        if (!file_exists($file_path)) {
            echo "<p style='color: orange;'>⚠️ الملف غير موجود: $file_path</p>";
            continue;
        }
        
        echo "<h3>فحص الملف: $file_path</h3>";
        
        // قراءة محتوى الملف
        $content = file_get_contents($file_path);
        $original_content = $content;
        
        // أنماط الأخطاء الشائعة وإصلاحها
        $error_patterns = array(
            // إصلاح متغيرات $_GET
            '/htmlspecialchars\(search\s*\?\?\s*[\'"][^\'"]*[\'"]\)/' => 'htmlspecialchars($_GET[\'search\'] ?? \'\')',
            '/htmlspecialchars\(date_from\s*\?\?\s*[\'"][^\'"]*[\'"]\)/' => 'htmlspecialchars($_GET[\'date_from\'] ?? \'\')',
            '/htmlspecialchars\(date_to\s*\?\?\s*[\'"][^\'"]*[\'"]\)/' => 'htmlspecialchars($_GET[\'date_to\'] ?? \'\')',
            '/htmlspecialchars\(customer_id\s*\?\?\s*[\'"][^\'"]*[\'"]\)/' => 'htmlspecialchars($_GET[\'customer_id\'] ?? \'\')',
            '/htmlspecialchars\(supplier_id\s*\?\?\s*[\'"][^\'"]*[\'"]\)/' => 'htmlspecialchars($_GET[\'supplier_id\'] ?? \'\')',
            '/htmlspecialchars\(category_id\s*\?\?\s*[\'"][^\'"]*[\'"]\)/' => 'htmlspecialchars($_GET[\'category_id\'] ?? \'\')',
            '/htmlspecialchars\(status\s*\?\?\s*[\'"][^\'"]*[\'"]\)/' => 'htmlspecialchars($_GET[\'status\'] ?? \'\')',
            
            // إصلاح متغيرات البيانات
            '/htmlspecialchars\(sale\[/' => 'htmlspecialchars($sale[',
            '/htmlspecialchars\(purchase\[/' => 'htmlspecialchars($purchase[',
            '/htmlspecialchars\(product\[/' => 'htmlspecialchars($product[',
            '/htmlspecialchars\(customer\[/' => 'htmlspecialchars($customer[',
            '/htmlspecialchars\(supplier\[/' => 'htmlspecialchars($supplier[',
            '/htmlspecialchars\(expense\[/' => 'htmlspecialchars($expense[',
            '/htmlspecialchars\(transaction\[/' => 'htmlspecialchars($transaction[',
            '/htmlspecialchars\(user\[/' => 'htmlspecialchars($user[',
            '/htmlspecialchars\(category\[/' => 'htmlspecialchars($category[',
            
            // إصلاح استخدام المتغيرات بدون $
            '/\bsale\[/' => '$sale[',
            '/\bpurchase\[/' => '$purchase[',
            '/\bproduct\[/' => '$product[',
            '/\bcustomer\[/' => '$customer[',
            '/\bsupplier\[/' => '$supplier[',
            '/\bexpense\[/' => '$expense[',
            '/\btransaction\[/' => '$transaction[',
            '/\buser\[/' => '$user[',
            '/\bcategory\[/' => '$category[',
            
            // إصلاح متغيرات GET بدون $
            '/\bsearch\s*\?\?\s*/' => '$_GET[\'search\'] ?? ',
            '/\bdate_from\s*\?\?\s*/' => '$_GET[\'date_from\'] ?? ',
            '/\bdate_to\s*\?\?\s*/' => '$_GET[\'date_to\'] ?? ',
            '/\bcustomer_id\s*\?\?\s*/' => '$_GET[\'customer_id\'] ?? ',
            '/\bsupplier_id\s*\?\?\s*/' => '$_GET[\'supplier_id\'] ?? ',
            '/\bcategory_id\s*\?\?\s*/' => '$_GET[\'category_id\'] ?? ',
            '/\bstatus\s*\?\?\s*/' => '$_GET[\'status\'] ?? '
        );
        
        $file_fixes = 0;
        
        foreach ($error_patterns as $pattern => $replacement) {
            $new_content = preg_replace($pattern, $replacement, $content);
            if ($new_content !== $content) {
                $matches = preg_match_all($pattern, $content);
                if ($matches > 0) {
                    $file_fixes += $matches;
                    $content = $new_content;
                    echo "<p style='color: blue;'>🔧 تم إصلاح نمط: " . htmlspecialchars($pattern) . "</p>";
                }
            }
        }
        
        // إصلاحات خاصة إضافية
        $special_fixes = array(
            // إصلاح استخدام value في input fields
            'value="<?php echo search' => 'value="<?php echo $_GET[\'search\']',
            'value="<?php echo date_from' => 'value="<?php echo $_GET[\'date_from\']',
            'value="<?php echo date_to' => 'value="<?php echo $_GET[\'date_to\']',
            'value="<?php echo customer_id' => 'value="<?php echo $_GET[\'customer_id\']',
            'value="<?php echo supplier_id' => 'value="<?php echo $_GET[\'supplier_id\']',
            'value="<?php echo category_id' => 'value="<?php echo $_GET[\'category_id\']',
            'value="<?php echo status' => 'value="<?php echo $_GET[\'status\']',
            
            // إصلاح selected في select options
            'selected="<?php echo search' => 'selected="<?php echo $_GET[\'search\']',
            'selected="<?php echo customer_id' => 'selected="<?php echo $_GET[\'customer_id\']',
            'selected="<?php echo supplier_id' => 'selected="<?php echo $_GET[\'supplier_id\']',
            'selected="<?php echo category_id' => 'selected="<?php echo $_GET[\'category_id\']',
            'selected="<?php echo status' => 'selected="<?php echo $_GET[\'status\']'
        );
        
        foreach ($special_fixes as $search => $replace) {
            if (strpos($content, $search) !== false) {
                $content = str_replace($search, $replace, $content);
                $file_fixes++;
                echo "<p style='color: blue;'>🔧 تم إصلاح: " . htmlspecialchars($search) . "</p>";
            }
        }
        
        // حفظ الملف إذا تم تعديله
        if ($content !== $original_content) {
            if (file_put_contents($file_path, $content)) {
                echo "<p style='color: green;'>✅ تم إصلاح $file_fixes خطأ في الملف</p>";
                $fixes_applied += $file_fixes;
            } else {
                echo "<p style='color: red;'>❌ فشل في حفظ الملف</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ لا توجد أخطاء ثوابت في هذا الملف</p>";
        }
        
        $files_processed++;
    }
    
    echo "<hr>";
    echo "<h2>تقرير الإصلاحات</h2>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>🎉 تم الانتهاء من إصلاح أخطاء الثوابت!</h3>";
    echo "<p><strong>الملفات المعالجة:</strong> $files_processed</p>";
    echo "<p><strong>الأخطاء المصلحة:</strong> $fixes_applied</p>";
    echo "</div>";
    
    // اختبار الصفحات المصلحة
    echo "<h2>اختبار الصفحات المصلحة</h2>";
    
    $test_pages = array(
        array('صفحة الخزينة', 'pages/treasury/index.php', '🏦'),
        array('عرض فاتورة مبيعات', 'pages/sales/view.php?id=1', '👁️'),
        array('قائمة المبيعات', 'pages/sales/index.php', '💰'),
        array('عرض فاتورة مشتريات', 'pages/purchases/view.php?id=1', '👁️'),
        array('قائمة المشتريات', 'pages/purchases/index.php', '🛒'),
        array('قائمة المنتجات', 'pages/products/index.php', '📦'),
        array('قائمة العملاء', 'pages/customers/index.php', '👥'),
        array('قائمة الموردين', 'pages/suppliers/index.php', '📋')
    );
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0;'>";
    
    foreach ($test_pages as $page) {
        echo "<div style='background: white; border: 1px solid #ddd; padding: 15px; text-align: center; border-radius: 8px;'>";
        echo "<div style='font-size: 2em; margin-bottom: 10px;'>{$page[2]}</div>";
        echo "<h4 style='margin: 10px 0;'>{$page[0]}</h4>";
        echo "<a href='{$page[1]}' target='_blank' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; display: inline-block;'>اختبار الصفحة</a>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // نصائح لتجنب الأخطاء مستقبلاً
    echo "<h2>نصائح لتجنب أخطاء الثوابت مستقبلاً</h2>";
    
    $tips = array(
        '✅ استخدم دائماً $ قبل أسماء المتغيرات في PHP',
        '✅ استخدم $_GET[\'key\'] للوصول إلى متغيرات GET',
        '✅ استخدم $_POST[\'key\'] للوصول إلى متغيرات POST',
        '✅ تحقق من وجود المتغيرات باستخدام isset() أو ?? قبل استخدامها',
        '✅ استخدم محرر نصوص يدعم تمييز أخطاء PHP',
        '✅ اختبر الصفحات بعد كل تعديل للتأكد من عدم وجود أخطاء'
    );
    
    echo "<ul style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; border-radius: 8px;'>";
    foreach ($tips as $tip) {
        echo "<li style='margin: 10px 0;'>$tip</li>";
    }
    echo "</ul>";
    
    echo "<h3>أمثلة على الاستخدام الصحيح:</h3>";
    
    echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>الاستخدام الصحيح:</h4>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;'>";
    echo htmlspecialchars('
// صحيح - استخدام متغيرات GET
value="<?php echo htmlspecialchars($_GET[\'search\'] ?? \'\'); ?>"

// صحيح - استخدام متغيرات البيانات
<?php echo htmlspecialchars($sale[\'sale_number\'] ?? \'\'); ?>

// صحيح - التحقق من وجود المتغير
<?php if (isset($_GET[\'search\']) && !empty($_GET[\'search\'])): ?>
    <p>البحث عن: <?php echo htmlspecialchars($_GET[\'search\']); ?></p>
<?php endif; ?>
');
    echo "</pre>";
    
    echo "<h4>الاستخدام الخاطئ:</h4>";
    echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 3px; overflow-x: auto;'>";
    echo htmlspecialchars('
// خطأ - استخدام ثابت بدلاً من متغير
value="<?php echo htmlspecialchars(search ?? \'\'); ?>"

// خطأ - نسيان $ قبل اسم المتغير
<?php echo htmlspecialchars(sale[\'sale_number\'] ?? \'\'); ?>

// خطأ - استخدام متغير غير معرف
<?php echo undefined_variable; ?>
');
    echo "</pre>";
    echo "</div>";
    
    echo "<h3>روابط سريعة للاختبار:</h3>";
    echo "<ul>";
    echo "<li><a href='pages/treasury/index.php' target='_blank'>اختبار صفحة الخزينة</a></li>";
    echo "<li><a href='pages/sales/view.php?id=1' target='_blank'>اختبار عرض فاتورة مبيعات</a></li>";
    echo "<li><a href='pages/sales/index.php' target='_blank'>اختبار قائمة المبيعات</a></li>";
    echo "<li><a href='pages/purchases/view.php?id=1' target='_blank'>اختبار عرض فاتورة مشتريات</a></li>";
    echo "<li><a href='final-error-check.php' target='_blank'>الفحص النهائي الشامل</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>
